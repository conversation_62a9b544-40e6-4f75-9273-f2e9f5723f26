# [DEPLOY-GUIDE-001] 部署指南

## 📋 概述

本文档提供了艾宾浩斯记忆曲线学习管理系统的完整部署指南，包括环境准备、部署步骤、配置说明和运维指导。

## 🏗️ 部署架构

### [DEPLOY-ARCH-001] 生产环境架构
```
┌─────────────────────────────────────────┐
│              负载均衡层                  │
│                Nginx                    │
└─────────────────┬───────────────────────┘
                  ↓
┌─────────────────────────────────────────┐
│              应用服务层                  │
│         Node.js + Express               │
│            (PM2管理)                    │
└─────────────────┬───────────────────────┘
                  ↓
┌─────────────────────────────────────────┐
│              数据存储层                  │
│      MongoDB + Redis + 文件存储         │
└─────────────────────────────────────────┘
```

## 🔧 环境要求

### [DEPLOY-REQ-001] 服务器要求
**最低配置**：
- CPU：2核心
- 内存：4GB RAM
- 存储：50GB SSD
- 网络：10Mbps带宽

**推荐配置**：
- CPU：4核心
- 内存：8GB RAM
- 存储：100GB SSD
- 网络：100Mbps带宽

### [DEPLOY-REQ-002] 软件环境
- **操作系统**：Ubuntu 20.04+ / CentOS 8+ / Amazon Linux 2
- **Node.js**：18+ LTS
- **MongoDB**：6.0+
- **Redis**：6.0+
- **Nginx**：1.20+
- **Docker**：20.10+ (可选)
- **Docker Compose**：2.0+ (可选)

## 🚀 部署方式

### 方式一：Docker容器化部署（推荐）

#### [DEPLOY-DOCKER-001] Docker部署步骤

**1. 准备部署文件**
```bash
# 克隆项目代码
git clone <repository-url>
cd ebbinghaus-learning-system

# 创建环境配置文件
cp .env.example .env
```

**2. 配置环境变量**
```bash
# 编辑 .env 文件
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://mongodb:27017/ebbinghaus
REDIS_URL=redis://redis:6379
JWT_SECRET=your-jwt-secret-key
```

**3. 构建和启动服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

**4. 初始化数据库**
```bash
# 执行数据库初始化脚本
docker-compose exec app npm run db:init
```

#### [DEPLOY-DOCKER-002] Docker Compose配置
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/ebbinghaus
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis
    restart: unless-stopped

  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

  redis:
    image: redis:6.0-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongodb_data:
```

### 方式二：传统部署

#### [DEPLOY-TRADITIONAL-001] 传统部署步骤

**1. 安装Node.js**
```bash
# 使用NodeSource仓库安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

**2. 安装MongoDB**
```bash
# 导入MongoDB公钥
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# 添加MongoDB仓库
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# 安装MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# 启动MongoDB服务
sudo systemctl start mongod
sudo systemctl enable mongod
```

**3. 安装Redis**
```bash
# 安装Redis
sudo apt-get install redis-server

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**4. 部署应用**
```bash
# 克隆代码
git clone <repository-url>
cd ebbinghaus-learning-system

# 安装依赖
npm install --production

# 构建前端
npm run build

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 安装PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js --env production

# 设置PM2开机自启
pm2 startup
pm2 save
```

## ⚙️ 配置说明

### [DEPLOY-CONFIG-001] 应用配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'ebbinghaus-app',
    script: './dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
  }]
}
```

### [DEPLOY-CONFIG-002] Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📊 监控和运维

### [DEPLOY-MONITOR-001] 系统监控
```bash
# PM2监控
pm2 monit

# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 重启应用
pm2 restart all

# 重载应用（零停机）
pm2 reload all
```

### [DEPLOY-MONITOR-002] 数据库监控
```bash
# MongoDB状态检查
mongo --eval "db.adminCommand('serverStatus')"

# Redis状态检查
redis-cli info

# 磁盘空间检查
df -h

# 内存使用检查
free -h
```

## 🔒 安全配置

### [DEPLOY-SECURITY-001] 基础安全
- 配置防火墙，只开放必要端口
- 使用SSL/TLS加密
- 定期更新系统和软件包
- 配置强密码策略
- 启用日志审计

### [DEPLOY-SECURITY-002] 应用安全
- 配置JWT密钥
- 启用CORS保护
- 配置请求限制
- 数据库访问控制
- 文件上传安全检查

## 🔗 相关文档

- [环境配置](./04-环境配置.md)
- [测试计划](./01-测试计划.md)
- [系统架构设计](../02-系统设计/01-系统整体架构设计.md)

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：运维工程师  
**审核人**：项目经理  
**状态**：草稿
