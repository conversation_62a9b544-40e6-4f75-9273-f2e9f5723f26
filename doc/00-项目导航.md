# 艾宾浩斯记忆曲线学习管理系统 - 项目导航

## 🧭 导航概述

本文档提供项目文档的全局导航，类似书籍目录，帮助用户快速定位所需信息。

## 📖 文档目录结构

```
doc/
├── README.md                           # 项目文档总览
├── 00-项目导航.md                      # 本文档 - 全局导航
├── 00-术语表.md                        # 统一术语定义
├── 00-临时备份/                        # 原始文档备份
│
├── 01-需求分析/                        # 需求分析阶段文档
│   ├── README.md                       # 需求分析总览
│   ├── 01-项目背景与目标.md            # 项目背景、目标用户、价值主张
│   ├── 02-功能需求规格.md              # 功能需求详细规格（REQ-FUNC-001~011）
│   ├── 03-用户场景与流程.md            # 用户场景和操作流程（REQ-SCENE-001~016）
│   ├── 04-非功能性需求.md              # 性能、安全、兼容性需求（REQ-NFUNC-001~025）
│   ├── 05-需求优先级与验收标准.md      # 需求优先级和验收标准
│   └── 06-业务规则索引.md              # 业务规则统一索引（REQ-RULE-001~599）
│
├── 02-系统设计/                        # 系统设计阶段文档
│   ├── README.md                       # 系统设计总览
│   ├── 01-系统整体架构设计.md          # 技术架构和部署方案
│   ├── 02-任务管理核心模块设计.md      # 任务管理模块详细设计
│   ├── 03-智能时间管理模块设计.md      # 时间管理模块详细设计
│   ├── 04-思维导图功能模块设计.md      # 思维导图模块详细设计
│   ├── 05-用户界面设计规范.md          # UI/UX设计规范
│   ├── 06-数据存储与同步设计.md        # 数据存储架构设计
│   ├── 07-模块协作与通信规范.md        # 模块间通信规范
│   ├── 08-API接口设计.md               # REST API接口规范（DES-API-001~009）
│   └── 09-数据模型设计.md              # 数据库模型设计（DES-MODEL-001~005）
│
├── 03-开发实现/                        # 开发实现阶段文档
│   ├── README.md                       # 开发实现总览
│   ├── 01-前端用户交互界面开发文档.md   # 前端开发规范和实现指南
│   └── 02-前端缺失功能补充开发文档.md   # 前端功能补充开发方案
│
├── 04-测试验证/                        # 测试验证阶段文档
│   ├── 01-测试计划.md                  # 测试策略和计划（TEST-PLAN-001）
│   └── 02-测试用例.md                  # 详细测试用例（TEST-CASE-001~007）
│
├── 05-部署运维/                        # 部署运维阶段文档
│   ├── README.md                       # 部署运维总览
│   ├── 03-部署指南.md                  # 部署流程和架构（DEPLOY-GUIDE-001）
│   └── 04-环境配置.md                  # 环境要求和配置（DEPLOY-REQ-001）
│
└── 06-项目管理/                        # 项目管理文档
    └── （待重组）                      # 文档规范、质量检查、自动化管理
```

## 🎯 按任务类型导航

### 📋 需求相关
| 任务 | 文档位置 | 关键编号 |
|------|----------|----------|
| 了解项目背景 | [01-需求分析/01-项目背景与目标.md](./01-需求分析/01-项目背景与目标.md) | REQ-BG-001 |
| 查看功能需求 | [01-需求分析/02-功能需求规格.md](./01-需求分析/02-功能需求规格.md) | REQ-FUNC-001~011 |
| 了解用户场景 | [01-需求分析/03-用户场景与流程.md](./01-需求分析/03-用户场景与流程.md) | REQ-SCENE-001~016 |
| 查看性能要求 | [01-需求分析/04-非功能性需求.md](./01-需求分析/04-非功能性需求.md) | REQ-NFUNC-001~025 |
| 确认验收标准 | [01-需求分析/05-需求优先级与验收标准.md](./01-需求分析/05-需求优先级与验收标准.md) | REQ-PRIORITY-001 |

### 🏗️ 设计相关
| 任务 | 文档位置 | 关键编号 |
|------|----------|----------|
| 了解系统架构 | [02-系统设计/01-系统整体架构设计.md](./02-系统设计/01-系统整体架构设计.md) | DES-ARCH-001 |
| 查看API接口 | [02-系统设计/08-API接口设计.md](./02-系统设计/08-API接口设计.md) | DES-API-001~009 |
| 了解数据模型 | [02-系统设计/09-数据模型设计.md](./02-系统设计/09-数据模型设计.md) | DES-MODEL-001~005 |
| 查看UI规范 | [02-系统设计/05-用户界面设计规范.md](./02-系统设计/05-用户界面设计规范.md) | DES-UI-001 |
| 模块通信规范 | [02-系统设计/07-模块协作与通信规范.md](./02-系统设计/07-模块协作与通信规范.md) | DES-COMM-001 |

### 🔧 开发相关
| 任务 | 文档位置 | 关键编号 |
|------|----------|----------|
| 前端开发规范 | [03-开发实现/01-前端用户交互界面开发文档.md](./03-开发实现/01-前端用户交互界面开发文档.md) | DEV-FRONTEND-001 |
| 前端功能补充 | [03-开发实现/02-前端缺失功能补充开发文档.md](./03-开发实现/02-前端缺失功能补充开发文档.md) | DEV-FRONTEND-002 |
| 开发环境配置 | [05-部署运维/04-环境配置.md](./05-部署运维/04-环境配置.md) | DEPLOY-REQ-001 |

### 🧪 测试相关
| 任务 | 文档位置 | 关键编号 |
|------|----------|----------|
| 制定测试计划 | [04-测试验证/01-测试计划.md](./04-测试验证/01-测试计划.md) | TEST-PLAN-001 |
| 编写测试用例 | [04-测试验证/02-测试用例.md](./04-测试验证/02-测试用例.md) | TEST-CASE-001~007 |
| 性能测试要求 | [01-需求分析/04-非功能性需求.md](./01-需求分析/04-非功能性需求.md) | REQ-NFUNC-001~010 |

### 🚀 部署相关
| 任务 | 文档位置 | 关键编号 |
|------|----------|----------|
| 环境搭建 | [05-部署运维/04-环境配置.md](./05-部署运维/04-环境配置.md) | DEPLOY-REQ-001 |
| 部署流程 | [05-部署运维/03-部署指南.md](./05-部署运维/03-部署指南.md) | DEPLOY-GUIDE-001 |
| 系统架构 | [02-系统设计/01-系统整体架构设计.md](./02-系统设计/01-系统整体架构设计.md) | DES-ARCH-001 |

## 🔍 按关键词搜索

### 核心概念
- **艾宾浩斯记忆曲线**：[术语表](./00-术语表.md#TERM-001) | [任务管理模块](./02-系统设计/02-任务管理核心模块设计.md)
- **负载均衡**：[术语表](./00-术语表.md#TERM-007) | [时间管理模块](./02-系统设计/03-智能时间管理模块设计.md)
- **思维导图**：[术语表](./00-术语表.md#TERM-011) | [思维导图模块](./02-系统设计/04-思维导图功能模块设计.md)
- **学习任务**：[术语表](./00-术语表.md#TERM-004) | [功能需求](./01-需求分析/02-功能需求规格.md#REQ-FUNC-001)

### 技术栈
- **Vue 3**：[系统架构](./02-系统设计/01-系统整体架构设计.md) | [UI设计规范](./02-系统设计/05-用户界面设计规范.md)
- **Node.js**：[系统架构](./02-系统设计/01-系统整体架构设计.md) | [API接口](./02-系统设计/08-API接口设计.md)
- **MongoDB**：[数据模型](./02-系统设计/09-数据模型设计.md) | [数据存储](./02-系统设计/06-数据存储与同步设计.md)
- **Docker**：[部署指南](./05-部署运维/03-部署指南.md) | [环境配置](./05-部署运维/04-环境配置.md)

### 功能模块
- **任务创建**：[REQ-FUNC-001](./01-需求分析/02-功能需求规格.md#REQ-FUNC-001) | [DES-API-001](./02-系统设计/08-API接口设计.md#DES-API-001)
- **复习计划**：[REQ-FUNC-002](./01-需求分析/02-功能需求规格.md#REQ-FUNC-002) | [算法设计](./02-系统设计/02-任务管理核心模块设计.md)
- **时间预估**：[REQ-FUNC-007](./01-需求分析/02-功能需求规格.md#REQ-FUNC-007) | [时间管理](./02-系统设计/03-智能时间管理模块设计.md)

## 📊 编号系统导航

### 需求编号 (REQ)
- **REQ-FUNC-001~011**：[功能需求规格](./01-需求分析/02-功能需求规格.md)
- **REQ-NFUNC-001~025**：[非功能性需求](./01-需求分析/04-非功能性需求.md)
- **REQ-SCENE-001~016**：[用户场景与流程](./01-需求分析/03-用户场景与流程.md)
- **REQ-RULE-001~599**：[业务规则索引](./01-需求分析/06-业务规则索引.md)

### 设计编号 (DES)
- **DES-ARCH-001**：[系统整体架构设计](./02-系统设计/01-系统整体架构设计.md)
- **DES-API-001~009**：[API接口设计](./02-系统设计/08-API接口设计.md)
- **DES-MODEL-001~005**：[数据模型设计](./02-系统设计/09-数据模型设计.md)
- **DES-UI-001**：[用户界面设计规范](./02-系统设计/05-用户界面设计规范.md)

### 开发编号 (DEV)
- **DEV-FRONTEND-001**：[前端用户交互界面开发文档](./03-开发实现/01-前端用户交互界面开发文档.md)
- **DEV-FRONTEND-002**：[前端缺失功能补充开发文档](./03-开发实现/02-前端缺失功能补充开发文档.md)

### 测试编号 (TEST)
- **TEST-PLAN-001**：[测试计划](./04-测试验证/01-测试计划.md)
- **TEST-CASE-001~007**：[测试用例](./04-测试验证/02-测试用例.md)

### 部署编号 (DEPLOY)
- **DEPLOY-GUIDE-001**：[部署指南](./05-部署运维/03-部署指南.md)
- **DEPLOY-REQ-001**：[环境配置](./05-部署运维/04-环境配置.md)

## 🚀 快速入口

### 新手入门
1. [项目文档总览](./README.md) - 项目整体了解
2. [项目背景与目标](./01-需求分析/01-项目背景与目标.md) - 项目背景
3. [术语表](./00-术语表.md) - 核心概念
4. 根据角色选择对应阅读路径

### 开发入门
1. [系统整体架构设计](./02-系统设计/01-系统整体架构设计.md) - 技术架构
2. [API接口设计](./02-系统设计/08-API接口设计.md) - 接口规范
3. [数据模型设计](./02-系统设计/09-数据模型设计.md) - 数据结构
4. [环境配置](./05-部署运维/04-环境配置.md) - 开发环境

### 测试入门
1. [功能需求规格](./01-需求分析/02-功能需求规格.md) - 测试范围
2. [需求优先级与验收标准](./01-需求分析/05-需求优先级与验收标准.md) - 验收标准
3. [测试计划](./04-测试验证/01-测试计划.md) - 测试策略
4. [测试用例](./04-测试验证/02-测试用例.md) - 具体用例

### 部署入门
1. [系统整体架构设计](./02-系统设计/01-系统整体架构设计.md) - 部署架构
2. [环境配置](./05-部署运维/04-环境配置.md) - 环境要求
3. [部署指南](./05-部署运维/03-部署指南.md) - 部署流程

## 📞 使用说明

### 导航使用技巧
1. **按流程导航**：从需求分析开始，按开发流程顺序阅读
2. **按角色导航**：根据自己的角色选择对应的阅读路径
3. **按任务导航**：根据当前要完成的任务快速定位文档
4. **按编号导航**：通过编号系统快速查找相关内容

### 文档更新
- 文档结构变更时，同步更新本导航文档
- 新增文档时，及时添加到相应分类
- 编号变更时，更新编号系统导航

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**维护团队**：项目文档团队  
**更新频率**：文档结构变更时同步更新
