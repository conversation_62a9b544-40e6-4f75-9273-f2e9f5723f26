# [DES-MODULE-003] 思维导图功能模块设计

## 📋 概述

本文档定义了思维导图功能模块的详细技术设计，包括 [TERM-011] 思维导图渲染技术、[TERM-013] 任务关联机制、图形交互和编辑功能的技术实现方案。

## 🎯 模块职责

### 核心功能
- **[TERM-011] 思维导图创建和编辑**：支持节点创建、编辑、删除、连接等基础操作
- **图形渲染和交互**：基于Cytoscape.js的高性能图形渲染和用户交互
- **[TERM-013] 任务关联机制**：思维导图节点与学习任务的关联和同步
- **布局算法**：自动布局和手动调整的混合布局系统
- **导入导出功能**：支持多种格式的思维导图导入导出

### 模块依赖
- **依赖模块**：任务管理核心模块（任务CRUD操作）、智能时间管理模块（负载检查）
- **被依赖模块**：用户界面模块（思维导图展示和交互）
- **第三方依赖**：Cytoscape.js（图形渲染引擎）

## 🎨 图形渲染设计

### [DES-UI-005] Cytoscape.js集成方案
**设计ID**：DES-UI-005  
**技术选型**：Cytoscape.js 3.26+  
**实现需求**：[REQ-FUNC-009] 思维导图创建功能

**集成架构**：
```typescript
// Vue组件封装Cytoscape.js
class MindMapRenderer {
  private cy: cytoscape.Core;
  private container: HTMLElement;
  private config: MindMapConfig;

  constructor(container: HTMLElement, config: MindMapConfig) {
    this.container = container;
    this.config = config;
    this.initializeCytoscape();
  }

  private initializeCytoscape(): void {
    this.cy = cytoscape({
      container: this.container,
      
      // 样式定义
      style: this.getStylesheet(),
      
      // 布局配置
      layout: {
        name: 'cose',
        animate: true,
        animationDuration: 500,
        fit: true,
        padding: 50,
        nodeRepulsion: 400000,
        nodeOverlap: 10,
        idealEdgeLength: 100,
        edgeElasticity: 100,
        nestingFactor: 5,
        gravity: 80,
        numIter: 1000,
        initialTemp: 200,
        coolingFactor: 0.95,
        minTemp: 1.0
      },

      // 交互配置
      userZoomingEnabled: true,
      userPanningEnabled: true,
      boxSelectionEnabled: true,
      selectionType: 'single',
      
      // 性能优化
      textureOnViewport: false,
      motionBlur: true,
      motionBlurOpacity: 0.2,
      wheelSensitivity: 0.1,
      pixelRatio: 'auto'
    });

    this.setupEventHandlers();
    this.setupExtensions();
  }

  private getStylesheet(): cytoscape.Stylesheet[] {
    return [
      // 节点样式
      {
        selector: 'node',
        style: {
          'background-color': '#4A90E2',
          'border-color': '#357ABD',
          'border-width': 2,
          'color': '#FFFFFF',
          'font-family': 'Arial, sans-serif',
          'font-size': '12px',
          'font-weight': 'bold',
          'text-valign': 'center',
          'text-halign': 'center',
          'text-wrap': 'wrap',
          'text-max-width': '120px',
          'width': 'label',
          'height': 'label',
          'padding': '10px',
          'shape': 'round-rectangle'
        }
      },
      
      // 中心节点样式
      {
        selector: 'node[type="center"]',
        style: {
          'background-color': '#E74C3C',
          'border-color': '#C0392B',
          'font-size': '16px',
          'width': '80px',
          'height': '80px'
        }
      },
      
      // 子节点样式
      {
        selector: 'node[type="child"]',
        style: {
          'background-color': '#2ECC71',
          'border-color': '#27AE60',
          'width': '60px',
          'height': '40px'
        }
      },
      
      // 叶子节点样式
      {
        selector: 'node[type="leaf"]',
        style: {
          'background-color': '#F39C12',
          'border-color': '#E67E22',
          'width': '50px',
          'height': '30px'
        }
      },
      
      // 连接线样式
      {
        selector: 'edge',
        style: {
          'width': 3,
          'line-color': '#95A5A6',
          'target-arrow-color': '#95A5A6',
          'target-arrow-shape': 'triangle',
          'curve-style': 'bezier',
          'control-point-step-size': 40
        }
      },
      
      // 选中状态样式
      {
        selector: ':selected',
        style: {
          'border-width': 4,
          'border-color': '#3498DB',
          'background-color': '#5DADE2'
        }
      },
      
      // 悬停状态样式
      {
        selector: 'node:active',
        style: {
          'overlay-color': '#3498DB',
          'overlay-padding': '10px',
          'overlay-opacity': 0.25
        }
      },
      
      // 任务关联节点样式
      {
        selector: 'node[hasTask]',
        style: {
          'border-style': 'dashed',
          'border-width': 3,
          'background-image': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjUgNEw2IDExLjVMMi41IDhMMSA5LjVMNiAxNC41TDE1IDIuNUwxMy41IDRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
          'background-image-opacity': 0.8
        }
      }
    ];
  }

  private setupEventHandlers(): void {
    // 节点点击事件
    this.cy.on('tap', 'node', (event) => {
      const node = event.target;
      this.handleNodeClick(node);
    });

    // 节点双击事件
    this.cy.on('dbltap', 'node', (event) => {
      const node = event.target;
      this.handleNodeDoubleClick(node);
    });

    // 节点右键菜单
    this.cy.on('cxttap', 'node', (event) => {
      const node = event.target;
      this.showContextMenu(node, event.renderedPosition);
    });

    // 画布点击事件
    this.cy.on('tap', (event) => {
      if (event.target === this.cy) {
        this.handleCanvasClick(event.renderedPosition);
      }
    });

    // 节点拖拽事件
    this.cy.on('dragfree', 'node', (event) => {
      const node = event.target;
      this.handleNodeDragEnd(node);
    });

    // 选择变化事件
    this.cy.on('select unselect', 'node', (event) => {
      this.handleSelectionChange();
    });
  }

  private setupExtensions(): void {
    // 上下文菜单扩展
    this.cy.contextMenus({
      menuItems: [
        {
          id: 'add-child',
          content: '添加子节点',
          tooltipText: '为当前节点添加子节点',
          selector: 'node',
          onClickFunction: (event) => {
            this.addChildNode(event.target);
          }
        },
        {
          id: 'edit-node',
          content: '编辑节点',
          tooltipText: '编辑节点内容',
          selector: 'node',
          onClickFunction: (event) => {
            this.editNode(event.target);
          }
        },
        {
          id: 'create-task',
          content: '创建任务',
          tooltipText: '基于此节点创建学习任务',
          selector: 'node',
          onClickFunction: (event) => {
            this.createTaskFromNode(event.target);
          }
        },
        {
          id: 'delete-node',
          content: '删除节点',
          tooltipText: '删除当前节点',
          selector: 'node',
          onClickFunction: (event) => {
            this.deleteNode(event.target);
          }
        }
      ]
    });

    // 撤销重做扩展
    this.cy.undoRedo({
      isDebug: false,
      actions: {},
      undoableDrag: true,
      stackSizeLimit: 50
    });

    // 网格对齐扩展
    this.cy.gridGuide({
      snapToGridOnRelease: true,
      snapToGridDuringDrag: false,
      snapToAlignmentLocationOnRelease: true,
      snapToAlignmentLocationDuringDrag: false,
      distributionGuidelines: true,
      geometricGuideline: true,
      initPosAlignment: true,
      centerToEdgeAlignment: true,
      resize: true,
      parentPadding: false,
      drawGrid: true,
      gridSpacing: 20,
      snapToGridCenter: false,
      zoomDash: true,
      panGrid: false,
      gridStackOrder: -1,
      gridColor: '#dedede',
      lineWidth: 1.0,
      guidelinesStackOrder: 4,
      guidelinesTolerance: 2.00,
      guidelinesStyle: {
        strokeStyle: "#8b7d6b",
        geometricGuidelineRange: 400,
        range: 100,
        minGuidelineLength: 10,
        direction: 1,
        horizontalDistColor: "#ff0000",
        verticalDistColor: "#00ff00",
        initPosAlignmentColor: "#0000ff",
        lineDash: [0, 0],
        horizontalDistLine: [0, 0],
        verticalDistLine: [0, 0],
        initPosAlignmentLine: [0, 0]
      }
    });
  }

  // 节点操作方法
  public addNode(data: NodeData, position?: Position): string {
    const nodeId = this.generateNodeId();
    
    this.cy.add({
      group: 'nodes',
      data: {
        id: nodeId,
        label: data.label,
        type: data.type || 'child',
        content: data.content || '',
        hasTask: false,
        taskId: null,
        ...data
      },
      position: position || this.getDefaultPosition()
    });

    this.cy.layout(this.config.layout).run();
    return nodeId;
  }

  public updateNode(nodeId: string, data: Partial<NodeData>): void {
    const node = this.cy.getElementById(nodeId);
    if (node.length > 0) {
      Object.keys(data).forEach(key => {
        node.data(key, data[key]);
      });
      
      // 触发重新渲染
      node.trigger('data');
    }
  }

  public deleteNode(nodeId: string): void {
    const node = this.cy.getElementById(nodeId);
    if (node.length > 0) {
      // 检查是否有关联任务
      if (node.data('hasTask')) {
        this.handleTaskAssociatedNodeDeletion(node);
      } else {
        this.cy.remove(node);
        this.cy.layout(this.config.layout).run();
      }
    }
  }

  public connectNodes(sourceId: string, targetId: string): string {
    const edgeId = this.generateEdgeId();
    
    this.cy.add({
      group: 'edges',
      data: {
        id: edgeId,
        source: sourceId,
        target: targetId
      }
    });

    return edgeId;
  }

  // 任务关联方法
  public associateTask(nodeId: string, taskId: string): void {
    const node = this.cy.getElementById(nodeId);
    if (node.length > 0) {
      node.data({
        hasTask: true,
        taskId: taskId
      });
      
      // 更新节点样式
      node.addClass('task-associated');
    }
  }

  public disassociateTask(nodeId: string): void {
    const node = this.cy.getElementById(nodeId);
    if (node.length > 0) {
      node.data({
        hasTask: false,
        taskId: null
      });
      
      // 移除样式类
      node.removeClass('task-associated');
    }
  }

  // 布局和视图控制
  public applyLayout(layoutName: string, options?: any): void {
    const layoutOptions = {
      name: layoutName,
      animate: true,
      animationDuration: 500,
      fit: true,
      ...options
    };

    this.cy.layout(layoutOptions).run();
  }

  public fitToView(): void {
    this.cy.fit(undefined, 50);
  }

  public centerOnNode(nodeId: string): void {
    const node = this.cy.getElementById(nodeId);
    if (node.length > 0) {
      this.cy.center(node);
    }
  }

  // 导入导出功能
  public exportToJSON(): MindMapData {
    return {
      nodes: this.cy.nodes().map(node => ({
        id: node.id(),
        data: node.data(),
        position: node.position()
      })),
      edges: this.cy.edges().map(edge => ({
        id: edge.id(),
        data: edge.data()
      })),
      style: this.cy.style().json(),
      metadata: {
        version: '1.0',
        createdAt: new Date().toISOString(),
        nodeCount: this.cy.nodes().length,
        edgeCount: this.cy.edges().length
      }
    };
  }

  public importFromJSON(data: MindMapData): void {
    // 清空当前图形
    this.cy.elements().remove();
    
    // 添加节点
    data.nodes.forEach(nodeData => {
      this.cy.add({
        group: 'nodes',
        data: nodeData.data,
        position: nodeData.position
      });
    });
    
    // 添加边
    data.edges.forEach(edgeData => {
      this.cy.add({
        group: 'edges',
        data: edgeData.data
      });
    });
    
    // 应用布局
    this.cy.layout(this.config.layout).run();
  }

  // 性能优化方法
  public enableBatchMode(): void {
    this.cy.startBatch();
  }

  public disableBatchMode(): void {
    this.cy.endBatch();
  }

  public optimizeForLargeGraphs(): void {
    // 对于大型图形的性能优化
    this.cy.textureOnViewport(true);
    this.cy.motionBlur(true);
    
    // 禁用动画以提升性能
    this.cy.autoungrabify(false);
    this.cy.autounselectify(false);
  }

  // 销毁方法
  public destroy(): void {
    if (this.cy) {
      this.cy.destroy();
    }
  }
}

// 数据接口定义
interface NodeData {
  label: string;
  type?: 'center' | 'child' | 'leaf';
  content?: string;
  color?: string;
  size?: number;
  hasTask?: boolean;
  taskId?: string;
}

interface Position {
  x: number;
  y: number;
}

interface MindMapData {
  nodes: Array<{
    id: string;
    data: any;
    position: Position;
  }>;
  edges: Array<{
    id: string;
    data: any;
  }>;
  style: any;
  metadata: {
    version: string;
    createdAt: string;
    nodeCount: number;
    edgeCount: number;
  };
}

interface MindMapConfig {
  layout: any;
  style: cytoscape.Stylesheet[];
  interaction: {
    zoomingEnabled: boolean;
    panningEnabled: boolean;
    boxSelectionEnabled: boolean;
  };
  performance: {
    textureOnViewport: boolean;
    motionBlur: boolean;
    wheelSensitivity: number;
  };
}
```

**性能优化策略**：
- **虚拟化渲染**：大型图形使用视口裁剪
- **批量操作**：多个操作合并为批量更新
- **纹理缓存**：启用纹理缓存提升渲染性能
- **动态LOD**：根据缩放级别调整细节层次

**实现需求**：[REQ-FUNC-009] 思维导图创建功能  
**相关设计**：[DES-MODEL-004] 思维导图数据模型

## 📊 数据模型设计

### [DES-MODEL-004] 思维导图数据模型
**模型ID**：DES-MODEL-004  
**模型名称**：MindMap 思维导图数据模型  
**实现需求**：[REQ-FUNC-009] 思维导图创建功能

**数据结构**：
```typescript
interface MindMap {
  // 基础信息
  id: string;                    // 思维导图ID
  title: string;                 // 导图标题
  description?: string;          // 导图描述
  
  // 图形数据
  nodes: MindMapNode[];          // 节点列表
  edges: MindMapEdge[];          // 连接列表
  
  // 布局信息
  layout: {
    type: string;                // 布局类型
    options: any;                // 布局参数
    viewport: {
      zoom: number;              // 缩放级别
      pan: { x: number; y: number }; // 平移位置
    };
  };
  
  // 样式配置
  theme: {
    name: string;                // 主题名称
    colors: ColorScheme;         // 颜色方案
    fonts: FontConfig;           // 字体配置
  };
  
  // 统计信息
  statistics: {
    nodeCount: number;           // 节点数量
    edgeCount: number;           // 连接数量
    maxDepth: number;            // 最大深度
    associatedTasks: number;     // 关联任务数
  };
  
  // 系统信息
  userId: string;                // 创建用户ID
  createdAt: Date;              // 创建时间
  updatedAt: Date;              // 更新时间
  version: number;              // 版本号
  isPublic: boolean;            // 是否公开
  tags: string[];               // 标签
}

interface MindMapNode {
  // 基础信息
  id: string;                   // 节点ID
  label: string;                // 节点标签
  content?: string;             // 节点内容
  
  // 层次信息
  type: NodeType;               // 节点类型
  level: number;                // 层级深度
  parentId?: string;            // 父节点ID
  childrenIds: string[];        // 子节点ID列表
  
  // 位置信息
  position: {
    x: number;                  // X坐标
    y: number;                  // Y坐标
  };
  
  // 样式信息
  style: {
    backgroundColor: string;     // 背景色
    borderColor: string;        // 边框色
    textColor: string;          // 文字色
    fontSize: number;           // 字体大小
    shape: string;              // 形状
    size: { width: number; height: number }; // 尺寸
  };
  
  // 任务关联
  taskAssociation?: {
    taskId: string;             // 关联任务ID
    associatedAt: Date;         // 关联时间
    syncStatus: 'synced' | 'pending' | 'conflict'; // 同步状态
  };
  
  // 多媒体内容
  media?: {
    images: string[];           // 图片URL列表
    links: string[];            // 链接列表
    attachments: string[];      // 附件列表
  };
  
  // 元数据
  metadata: {
    createdAt: Date;            // 创建时间
    updatedAt: Date;            // 更新时间
    isLocked: boolean;          // 是否锁定
    priority: number;           // 优先级
  };
}

interface MindMapEdge {
  // 基础信息
  id: string;                   // 连接ID
  sourceId: string;             // 源节点ID
  targetId: string;             // 目标节点ID
  
  // 连接类型
  type: EdgeType;               // 连接类型
  direction: 'directed' | 'undirected'; // 方向性
  
  // 样式信息
  style: {
    color: string;              // 线条颜色
    width: number;              // 线条宽度
    pattern: 'solid' | 'dashed' | 'dotted'; // 线条样式
    arrowType: string;          // 箭头类型
  };
  
  // 标签信息
  label?: string;               // 连接标签
  labelPosition: 'source' | 'target' | 'middle'; // 标签位置
  
  // 元数据
  metadata: {
    createdAt: Date;            // 创建时间
    weight: number;             // 权重
    isVisible: boolean;         // 是否可见
  };
}

// 枚举定义
enum NodeType {
  CENTER = 'center',            // 中心节点
  MAIN_BRANCH = 'main_branch',  // 主分支
  SUB_BRANCH = 'sub_branch',    // 子分支
  LEAF = 'leaf'                 // 叶子节点
}

enum EdgeType {
  HIERARCHY = 'hierarchy',      // 层次关系
  ASSOCIATION = 'association',  // 关联关系
  DEPENDENCY = 'dependency',    // 依赖关系
  REFERENCE = 'reference'       // 引用关系
}

interface ColorScheme {
  primary: string;              // 主色
  secondary: string;            // 辅色
  accent: string;               // 强调色
  background: string;           // 背景色
  text: string;                 // 文字色
  border: string;               // 边框色
}

interface FontConfig {
  family: string;               // 字体族
  sizes: {
    center: number;             // 中心节点字体大小
    branch: number;             // 分支节点字体大小
    leaf: number;               // 叶子节点字体大小
  };
  weights: {
    normal: number;             // 普通字重
    bold: number;               // 粗体字重
  };
}
```

**数据约束**：
- **主键约束**：id字段唯一
- **外键约束**：userId必须存在于用户表
- **层次约束**：parentId必须指向有效节点
- **位置约束**：position坐标必须为有效数值
- **关联约束**：taskId必须存在于任务表

**索引设计**：
- **主键索引**：id
- **复合索引**：userId + createdAt（用户导图列表）
- **复合索引**：userId + title（导图搜索）
- **外键索引**：taskId（任务关联查询）

**实现需求**：[REQ-FUNC-009] 思维导图创建功能  
**相关设计**：[DES-UI-005] Cytoscape.js集成方案

### [DES-MODEL-005] 任务关联数据模型
**模型ID**：DES-MODEL-005  
**模型名称**：TaskNodeAssociation 任务节点关联模型  
**实现需求**：[REQ-FUNC-010] 思维导图任务关联功能

**数据结构**：
```typescript
interface TaskNodeAssociation {
  // 基础信息
  id: string;                   // 关联ID
  taskId: string;               // 任务ID
  nodeId: string;               // 节点ID
  mindMapId: string;            // 思维导图ID
  
  // 关联类型
  associationType: AssociationType; // 关联类型
  direction: 'task_to_node' | 'node_to_task'; // 创建方向
  
  // 同步配置
  syncConfig: {
    autoSync: boolean;          // 自动同步
    syncFields: string[];       // 同步字段
    conflictResolution: 'manual' | 'task_priority' | 'node_priority'; // 冲突解决
  };
  
  // 状态信息
  status: {
    syncStatus: SyncStatus;     // 同步状态
    lastSyncAt?: Date;          // 最后同步时间
    conflictCount: number;      // 冲突次数
    errorMessage?: string;      // 错误信息
  };
  
  // 映射配置
  fieldMapping: {
    taskTitle: string;          // 任务标题映射到节点字段
    taskContent: string;        // 任务内容映射到节点字段
    taskStatus: string;         // 任务状态映射到节点字段
    taskPriority: string;       // 任务优先级映射到节点字段
  };
  
  // 系统信息
  userId: string;               // 用户ID
  createdAt: Date;              // 创建时间
  updatedAt: Date;              // 更新时间
  createdBy: 'user' | 'system'; // 创建方式
}

// 关联类型枚举
enum AssociationType {
  DIRECT = 'direct',            // 直接关联
  DERIVED = 'derived',          // 派生关联
  REFERENCE = 'reference'       // 引用关联
}

// 同步状态枚举
enum SyncStatus {
  SYNCED = 'synced',           // 已同步
  PENDING = 'pending',         // 待同步
  CONFLICT = 'conflict',       // 冲突
  ERROR = 'error',             // 错误
  DISABLED = 'disabled'        // 已禁用
}

// 同步操作记录
interface SyncOperation {
  id: string;                   // 操作ID
  associationId: string;        // 关联ID
  operationType: 'create' | 'update' | 'delete'; // 操作类型
  sourceType: 'task' | 'node';  // 源类型
  changes: {
    field: string;              // 变更字段
    oldValue: any;              // 旧值
    newValue: any;              // 新值
  }[];
  status: 'success' | 'failed' | 'pending'; // 操作状态
  executedAt: Date;             // 执行时间
  errorMessage?: string;        // 错误信息
}
```

**数据约束**：
- **主键约束**：id字段唯一
- **外键约束**：taskId、nodeId、mindMapId必须存在
- **唯一约束**：taskId + nodeId组合唯一
- **检查约束**：syncFields必须为有效字段名

**索引设计**：
- **主键索引**：id
- **唯一索引**：taskId + nodeId
- **复合索引**：mindMapId + syncStatus（同步查询）
- **复合索引**：userId + createdAt（用户关联历史）

**实现需求**：[REQ-FUNC-010] 思维导图任务关联功能  
**相关设计**：[DES-API-006] 任务关联API接口

## 🔌 API接口设计

### [DES-API-005] 思维导图管理API接口
**接口ID**：DES-API-005  
**接口类型**：RESTful API  
**实现需求**：[REQ-FUNC-009] 思维导图创建功能

#### 创建思维导图接口
**接口路径**：POST /api/mindmaps  
**请求格式**：
```json
{
  "title": "数学-二次函数知识图谱",
  "description": "二次函数相关知识点整理",
  "template": "academic",
  "initialNodes": [
    {
      "label": "二次函数",
      "type": "center",
      "content": "形如y=ax²+bx+c的函数"
    }
  ],
  "theme": {
    "name": "default",
    "colors": {
      "primary": "#4A90E2",
      "secondary": "#2ECC71"
    }
  }
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "mindMapId": "uuid",
    "title": "数学-二次函数知识图谱",
    "nodeCount": 1,
    "edgeCount": 0,
    "createdAt": "2025-01-31T10:00:00Z",
    "editUrl": "/mindmap/edit/uuid",
    "viewUrl": "/mindmap/view/uuid"
  },
  "error": null
}
```

#### 更新思维导图接口
**接口路径**：PUT /api/mindmaps/:mindMapId  
**请求格式**：
```json
{
  "operations": [
    {
      "type": "add_node",
      "data": {
        "label": "函数图像",
        "parentId": "center-node-id",
        "position": { "x": 100, "y": 50 }
      }
    },
    {
      "type": "update_node",
      "nodeId": "node-id",
      "data": {
        "label": "新标签",
        "content": "更新的内容"
      }
    },
    {
      "type": "add_edge",
      "data": {
        "sourceId": "node1",
        "targetId": "node2",
        "type": "hierarchy"
      }
    }
  ],
  "layout": {
    "type": "cose",
    "animate": true
  }
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "operationsApplied": 3,
    "newNodeIds": ["new-node-1"],
    "newEdgeIds": ["new-edge-1"],
    "updatedAt": "2025-01-31T10:05:00Z",
    "version": 2
  },
  "error": null
}
```

#### 获取思维导图接口
**接口路径**：GET /api/mindmaps/:mindMapId  
**查询参数**：
- `includeNodes`: 是否包含节点数据
- `includeEdges`: 是否包含连接数据
- `format`: 返回格式（json|cytoscape）

**响应格式**：
```json
{
  "success": true,
  "data": {
    "mindMap": {
      "id": "uuid",
      "title": "数学-二次函数知识图谱",
      "nodes": [...],
      "edges": [...],
      "layout": {...},
      "theme": {...},
      "statistics": {
        "nodeCount": 15,
        "edgeCount": 14,
        "maxDepth": 3,
        "associatedTasks": 5
      }
    }
  },
  "error": null
}
```

**实现需求**：[REQ-FUNC-009] 思维导图创建功能  
**相关模型**：[DES-MODEL-004] 思维导图数据模型

### [DES-API-006] 任务关联API接口
**接口ID**：DES-API-006  
**接口类型**：RESTful API  
**实现需求**：[REQ-FUNC-010] 思维导图任务关联功能

#### 创建任务关联接口
**接口路径**：POST /api/mindmaps/:mindMapId/nodes/:nodeId/associate-task  
**请求格式**：
```json
{
  "associationType": "direct",
  "taskData": {
    "title": "学习二次函数图像",
    "content": "掌握二次函数图像的特点和画法",
    "subject": "math",
    "estimatedTime": 45,
    "priority": 4,
    "difficulty": 3
  },
  "syncConfig": {
    "autoSync": true,
    "syncFields": ["title", "content", "status"],
    "conflictResolution": "manual"
  }
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "associationId": "uuid",
    "taskId": "uuid",
    "nodeId": "uuid",
    "syncStatus": "synced",
    "loadWarning": {
      "level": "medium",
      "message": "当日学习负载适中",
      "suggestions": []
    },
    "createdAt": "2025-01-31T10:00:00Z"
  },
  "error": null
}
```

#### 批量创建任务接口
**接口路径**：POST /api/mindmaps/:mindMapId/batch-create-tasks  
**请求格式**：
```json
{
  "nodeIds": ["node1", "node2", "node3"],
  "taskTemplate": {
    "subject": "math",
    "priority": 3,
    "estimatedTime": 30
  },
  "scheduleOptions": {
    "startDate": "2025-02-01",
    "distributeDays": 7,
    "checkLoadBalance": true
  },
  "syncConfig": {
    "autoSync": true,
    "conflictResolution": "task_priority"
  }
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "createdTasks": [
      {
        "nodeId": "node1",
        "taskId": "task1",
        "associationId": "assoc1",
        "scheduledDate": "2025-02-01"
      }
    ],
    "skippedNodes": [
      {
        "nodeId": "node4",
        "reason": "ALREADY_HAS_TASK",
        "existingTaskId": "existing-task"
      }
    ],
    "loadAnalysis": {
      "totalTasks": 3,
      "averageLoad": 45,
      "peakDate": "2025-02-03",
      "suggestions": ["建议调整任务2的时间安排"]
    }
  },
  "error": null
}
```

#### 同步状态查询接口
**接口路径**：GET /api/associations/:associationId/sync-status  
**响应格式**：
```json
{
  "success": true,
  "data": {
    "associationId": "uuid",
    "syncStatus": "synced",
    "lastSyncAt": "2025-01-31T10:00:00Z",
    "conflictCount": 0,
    "pendingChanges": [],
    "syncHistory": [
      {
        "timestamp": "2025-01-31T10:00:00Z",
        "operation": "update",
        "field": "title",
        "status": "success"
      }
    ]
  },
  "error": null
}
```

#### 解决同步冲突接口
**接口路径**：POST /api/associations/:associationId/resolve-conflict  
**请求格式**：
```json
{
  "resolution": "manual",
  "fieldResolutions": [
    {
      "field": "title",
      "selectedValue": "task_value",
      "reason": "任务标题更准确"
    },
    {
      "field": "content",
      "selectedValue": "node_value",
      "reason": "节点内容更详细"
    }
  ]
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "resolvedFields": 2,
    "syncStatus": "synced",
    "appliedChanges": [
      {
        "field": "title",
        "finalValue": "学习二次函数图像",
        "source": "task"
      }
    ],
    "resolvedAt": "2025-01-31T10:05:00Z"
  },
  "error": null
}
```

**实现需求**：[REQ-FUNC-010] 思维导图任务关联功能  
**相关模型**：[DES-MODEL-005] 任务关联数据模型

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 | 批准人 |
|------|------|----------|--------|--------|
| v1.0 | 2025-01-31 | 初始思维导图功能模块设计创建 | 前端架构师 | 技术负责人 |

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：前端架构师  
**审核人**：技术负责人  
**状态**：待审核
