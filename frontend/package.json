{"name": "ebbinghaus-learning-frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "clean:all": "rm -rf dist node_modules package-lock.json", "reinstall": "npm run clean:all && npm install", "check": "npm run lint:check && npm run type-check && npm run test:run", "check:fix": "npm run lint && npm run format && npm run type-check", "audit:fix": "npm audit fix", "outdated": "npm outdated", "update:deps": "npm update"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@pinia/nuxt": "^0.11.2", "@vueuse/core": "^13.6.0", "axios": "^1.11.0", "cytoscape": "^3.33.0", "cytoscape-cola": "^2.5.1", "cytoscape-cose-bilkent": "^4.1.0", "cytoscape-dagre": "^2.5.0", "cytoscape-fcose": "^2.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "sass": "^1.89.2", "vue": "^3.5.18", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@playwright/test": "^1.48.2", "@tailwindcss/postcss": "^4.1.11", "@tsconfig/node22": "^22.0.2", "@types/cytoscape": "^3.21.9", "@types/node": "^22.16.5", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/ui": "^2.1.8", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "c8": "^10.1.2", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.4.0", "happy-dom": "^15.11.6", "jsdom": "^25.0.1", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^2.1.8", "vue-tsc": "^3.0.4"}}