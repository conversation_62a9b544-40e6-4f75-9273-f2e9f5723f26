import { FullConfig, chromium } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...')

  // 启动浏览器进行预热
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // 等待开发服务器启动
    console.log('⏳ Waiting for development server...')
    
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:5173'
    let retries = 0
    const maxRetries = 30

    while (retries < maxRetries) {
      try {
        const response = await page.goto(baseURL, { timeout: 5000 })
        if (response && response.ok()) {
          console.log('✅ Development server is ready!')
          break
        }
      } catch (error) {
        retries++
        console.log(`⏳ Waiting for server... (${retries}/${maxRetries})`)
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }

    if (retries >= maxRetries) {
      throw new Error('❌ Development server failed to start within timeout')
    }

    // 预热应用
    console.log('🔥 Warming up application...')
    
    // 访问主要页面以预加载资源
    const pages = ['/', '/tasks', '/review', '/mindmap']
    
    for (const pagePath of pages) {
      try {
        await page.goto(`${baseURL}${pagePath}`, { 
          timeout: 10000,
          waitUntil: 'networkidle' 
        })
        console.log(`✅ Preloaded: ${pagePath}`)
      } catch (error) {
        console.log(`⚠️  Failed to preload: ${pagePath}`)
      }
    }

    // 设置测试数据（如果需要）
    console.log('📊 Setting up test data...')
    
    // 这里可以添加测试数据的设置逻辑
    // 例如：清理localStorage、设置mock数据等
    await page.evaluate(() => {
      // 清理本地存储
      localStorage.clear()
      sessionStorage.clear()
      
      // 设置测试环境标识
      localStorage.setItem('test-mode', 'true')
      
      // 设置mock数据
      const mockTasks = [
        {
          id: 'test-task-1',
          title: 'E2E测试任务1',
          subject: '数学',
          status: 'active',
          difficulty: 3,
          priority: 2,
          estimatedTime: 30,
          createdAt: new Date().toISOString()
        },
        {
          id: 'test-task-2',
          title: 'E2E测试任务2',
          subject: '英语',
          status: 'completed',
          difficulty: 2,
          priority: 1,
          estimatedTime: 45,
          createdAt: new Date().toISOString()
        }
      ]
      
      localStorage.setItem('test-tasks', JSON.stringify(mockTasks))
    })

    console.log('✅ Global setup completed successfully!')

  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup
