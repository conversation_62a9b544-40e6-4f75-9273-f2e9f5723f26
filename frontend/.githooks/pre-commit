#!/bin/sh
# Pre-commit hook for frontend development

echo "🔍 Running pre-commit checks..."

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
  echo "❌ Not in a Node.js project directory"
  exit 1
fi

# Run ESLint
echo "📝 Running ESLint..."
npm run lint:check
if [ $? -ne 0 ]; then
  echo "❌ ESLint failed. Please fix the issues and try again."
  echo "💡 You can run 'npm run lint' to auto-fix some issues."
  exit 1
fi

# Run Prettier check
echo "🎨 Checking code formatting..."
npm run format:check
if [ $? -ne 0 ]; then
  echo "❌ Code formatting issues found. Please fix them and try again."
  echo "💡 You can run 'npm run format' to auto-format your code."
  exit 1
fi

# Run TypeScript type checking
echo "🔧 Running TypeScript type checking..."
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ TypeScript type checking failed. Please fix the issues and try again."
  exit 1
fi

# Run unit tests
echo "🧪 Running unit tests..."
npm run test:run
if [ $? -ne 0 ]; then
  echo "❌ Unit tests failed. Please fix the failing tests and try again."
  exit 1
fi

echo "✅ All pre-commit checks passed!"
exit 0
