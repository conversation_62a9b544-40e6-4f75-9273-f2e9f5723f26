import { beforeEach, describe, expect, it, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import Dashboard from '../Dashboard.vue'

// Mock ECharts
vi.mock('echarts', () => ({
  default: {
    init: vi.fn(() => ({
      setOption: vi.fn(),
      resize: vi.fn(),
      dispose: vi.fn()
    })),
    registerTheme: vi.fn()
  }
}))

// Mock stores
const mockTaskStore = {
  tasks: [
    {
      id: '1',
      title: 'Test Task 1',
      subject: '数学',
      status: 'active',
      difficulty: 3,
      priority: 2,
      estimatedTime: 30,
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      title: 'Test Task 2',
      subject: '英语',
      status: 'completed',
      difficulty: 2,
      priority: 1,
      estimatedTime: 45,
      createdAt: '2024-01-02T00:00:00Z'
    }
  ],
  loading: false,
  error: null,
  getTotalTasks: vi.fn(() => 2),
  getActiveTasks: vi.fn(() => 1),
  getCompletedTasks: vi.fn(() => 1),
  fetchTasks: vi.fn()
}

const mockReviewStore = {
  reviews: [
    {
      id: '1',
      taskId: '1',
      completedAt: '2024-01-01T10:00:00Z',
      quality: 4,
      actualTime: 25
    }
  ],
  loading: false,
  error: null,
  getReviewPlans: vi.fn(() => [
    {
      id: '1',
      taskId: '1',
      nextReviewTime: '2024-01-02T10:00:00Z',
      reviewCount: 1
    }
  ]),
  fetchReviews: vi.fn()
}

vi.mock('@/stores/task', () => ({
  useTaskStore: () => mockTaskStore
}))

vi.mock('@/stores/review', () => ({
  useReviewStore: () => mockReviewStore
}))

describe('Dashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render dashboard correctly', () => {
    const wrapper = mount(Dashboard)

    expect(wrapper.find('.dashboard').exists()).toBe(true)
    expect(wrapper.find('.page-header').exists()).toBe(true)
    expect(wrapper.text()).toContain('学习仪表板')
  })

  it('should display statistics cards', () => {
    const wrapper = mount(Dashboard)

    const statsCards = wrapper.findAll('.stat-card')
    expect(statsCards.length).toBeGreaterThan(0)

    // 检查是否显示任务统计
    expect(wrapper.text()).toContain('总任务数')
    expect(wrapper.text()).toContain('活跃任务')
    expect(wrapper.text()).toContain('已完成')
  })

  it('should show correct task statistics', () => {
    const wrapper = mount(Dashboard)

    // 验证统计数据是否正确显示
    expect(mockTaskStore.getTotalTasks).toHaveBeenCalled()
    expect(mockTaskStore.getActiveTasks).toHaveBeenCalled()
    expect(mockTaskStore.getCompletedTasks).toHaveBeenCalled()
  })

  it('should display recent activities', () => {
    const wrapper = mount(Dashboard)

    // 查找活动相关的元素
    const activitySection = wrapper.find('.recent-activities')
    if (activitySection.exists()) {
      expect(activitySection.exists()).toBe(true)
    }
  })

  it('should handle loading state', async () => {
    // 模拟加载状态
    mockTaskStore.loading = true
    mockReviewStore.loading = true

    const wrapper = mount(Dashboard)

    // 检查是否显示加载状态
    const loadingElements = wrapper.findAll('[loading="true"]')
    expect(loadingElements.length).toBeGreaterThan(0)
  })

  it('should handle error state', async () => {
    // 模拟错误状态
    mockTaskStore.error = 'Failed to load tasks'
    mockReviewStore.error = 'Failed to load reviews'

    const wrapper = mount(Dashboard)

    // 检查是否处理错误状态
    // 这取决于具体的错误处理实现
    expect(wrapper.exists()).toBe(true)
  })

  it('should fetch data on mount', () => {
    mount(Dashboard)

    expect(mockTaskStore.fetchTasks).toHaveBeenCalled()
    expect(mockReviewStore.fetchReviews).toHaveBeenCalled()
  })

  it('should display charts section', () => {
    const wrapper = mount(Dashboard)

    // 查找图表相关的元素
    const chartsSection = wrapper.find('.charts-section')
    if (chartsSection.exists()) {
      expect(chartsSection.exists()).toBe(true)
    }
  })

  it('should be responsive', () => {
    const wrapper = mount(Dashboard)

    // 检查是否有响应式相关的类或结构
    const responsiveElements = wrapper.findAll('.el-row, .el-col')
    expect(responsiveElements.length).toBeGreaterThan(0)
  })

  it('should handle empty data gracefully', async () => {
    // 模拟空数据
    mockTaskStore.tasks = []
    mockReviewStore.reviews = []
    mockTaskStore.getTotalTasks = vi.fn(() => 0)
    mockTaskStore.getActiveTasks = vi.fn(() => 0)
    mockTaskStore.getCompletedTasks = vi.fn(() => 0)

    const wrapper = mount(Dashboard)

    // 应该能正常渲染，不会崩溃
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.dashboard').exists()).toBe(true)
  })

  it('should update when store data changes', async () => {
    const wrapper = mount(Dashboard)

    // 模拟数据更新
    mockTaskStore.tasks.push({
      id: '3',
      title: 'New Task',
      subject: '物理',
      status: 'active',
      difficulty: 4,
      priority: 3,
      estimatedTime: 60,
      createdAt: '2024-01-03T00:00:00Z'
    })

    mockTaskStore.getTotalTasks = vi.fn(() => 3)
    mockTaskStore.getActiveTasks = vi.fn(() => 2)

    await wrapper.vm.$nextTick()

    // 验证组件是否响应数据变化
    expect(wrapper.exists()).toBe(true)
  })

  it('should have proper accessibility attributes', () => {
    const wrapper = mount(Dashboard)

    // 检查是否有适当的可访问性属性
    const headings = wrapper.findAll('h1, h2, h3, h4, h5, h6')
    expect(headings.length).toBeGreaterThan(0)

    // 检查是否有适当的语义化标签
    const mainContent = wrapper.find('main, .main-content, .dashboard')
    expect(mainContent.exists()).toBe(true)
  })

  it('should handle navigation correctly', () => {
    const wrapper = mount(Dashboard)

    // 查找可能的导航链接或按钮
    const navigationElements = wrapper.findAll('a, button, .router-link')
    
    // 至少应该有一些交互元素
    expect(navigationElements.length).toBeGreaterThanOrEqual(0)
  })
})
