<template>
  <div class="mindmap-page">
    <div class="page-header">
      <div class="header-left">
        <h2>思维导图</h2>
        <p>可视化知识结构，提升学习效果</p>
      </div>
      <div class="header-actions">
        <el-button :icon="Plus" type="primary" @click="createMindMap">
          创建导图
        </el-button>
        <el-button :icon="Upload" @click="importMindMap">
          导入
        </el-button>
        <el-button :icon="Setting" @click="showSettings = true">
          设置
        </el-button>
      </div>
    </div>

    <!-- 工具栏 -->
    <div v-if="currentMindMap" class="toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button
            :type="viewMode === 'canvas' ? 'primary' : 'default'"
            title="画布模式"
            @click="viewMode = 'canvas'"
          >
            画布
          </el-button>
          <el-button
            :type="viewMode === 'cytoscape' ? 'primary' : 'default'"
            title="图形模式"
            @click="viewMode = 'cytoscape'"
          >
            图形
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button :icon="ZoomIn" title="放大" @click="zoomIn" />
          <el-button :icon="ZoomOut" title="缩小" @click="zoomOut" />
          <el-button :icon="Refresh" title="重置缩放" @click="resetZoom" />
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button :icon="Plus" title="添加节点" @click="addNode" />
          <el-button :icon="Delete" :disabled="!selectedNode" title="删除节点" @click="deleteNode" />
          <el-button :icon="Edit" :disabled="!selectedNode" title="编辑节点" @click="editNode" />
          <el-button :icon="Link" :disabled="!selectedNode" title="关联任务" @click="linkTask" />
        </el-button-group>

        <el-divider direction="vertical" />

        <el-select v-model="currentLayout" style="width: 120px" @change="changeLayout">
          <el-option label="树形布局" value="tree" />
          <el-option label="径向布局" value="radial" />
          <el-option label="力导向布局" value="force" />
          <el-option label="层次布局" value="hierarchical" />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button :icon="Download" @click="exportMindMap">导出</el-button>
        <el-button :icon="Share" @click="shareMindMap">分享</el-button>
        <el-button type="primary" :icon="Check" @click="saveMindMap">保存</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="mindmap-content">
      <!-- 思维导图列表 -->
      <div v-if="!currentMindMap" class="mindmap-list">
        <div class="list-header">
          <h3>我的思维导图</h3>
          <div class="list-controls">
            <el-input
              v-model="searchText"
              placeholder="搜索思维导图..."
              :prefix-icon="Search"
              style="width: 300px"
            />
            <el-select v-model="sortBy" style="width: 120px">
              <el-option label="最近修改" value="updated" />
              <el-option label="创建时间" value="created" />
              <el-option label="名称" value="name" />
            </el-select>
          </div>
        </div>

        <div v-if="filteredMindMaps.length === 0" class="empty-state">
          <el-empty description="暂无思维导图">
            <el-button type="primary" @click="createMindMap">
              创建第一个思维导图
            </el-button>
          </el-empty>
        </div>

        <div v-else class="mindmap-grid">
          <div
            v-for="mindmap in filteredMindMaps"
            :key="mindmap.id"
            class="mindmap-card"
            @click="openMindMap(mindmap)"
          >
            <div class="card-preview">
              <div class="preview-placeholder">
                <el-icon><Share /></el-icon>
              </div>
            </div>
            <div class="card-content">
              <h4 class="card-title">{{ mindmap.title }}</h4>
              <p class="card-description">{{ mindmap.description }}</p>
              <div class="card-meta">
                <span class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  {{ formatDate(mindmap.updatedAt) }}
                </span>
                <span class="meta-item">
                  <el-icon><User /></el-icon>
                  {{ mindmap.nodeCount }}个节点
                </span>
              </div>
            </div>
            <div class="card-actions">
              <el-button size="small" :icon="Edit" @click.stop="editMindMapInfo(mindmap)" />
              <el-button size="small" :icon="Share" @click.stop="shareMindMap" />
              <el-button size="small" :icon="Delete" type="danger" @click.stop="deleteMindMap(mindmap)" />
            </div>
          </div>
        </div>
      </div>

      <!-- 思维导图编辑器 -->
      <div v-else class="mindmap-editor">
        <div class="editor-sidebar">
          <div class="sidebar-header">
            <h4>{{ currentMindMap.title }}</h4>
            <el-button size="small" :icon="Close" @click="closeMindMap" />
          </div>

          <el-tabs v-model="activeTab" class="sidebar-tabs">
            <el-tab-pane label="节点" name="nodes">
              <div class="nodes-panel">
                <div class="panel-header">
                  <span>节点列表</span>
                  <el-button size="small" :icon="Plus" @click="addNode" />
                </div>
                <div class="nodes-tree">
                  <MindMapNodeTree
                    :nodes="currentMindMap.nodes"
                    :selected-node="selectedNode"
                    @select-node="selectNode"
                    @edit-node="editNode"
                    @delete-node="deleteNode"
                  />
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="样式" name="style">
              <div class="style-panel">
                <MindMapStylePanel
                  :selected-node="selectedNode"
                  @update-style="updateNodeStyle"
                />
              </div>
            </el-tab-pane>

            <el-tab-pane label="设置" name="settings">
              <div class="settings-panel">
                <MindMapSettings
                  :mindmap="currentMindMap"
                  @update-settings="updateMindMapSettings"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div class="editor-canvas">
          <!-- 画布模式 -->
          <MindMapCanvas
            v-if="viewMode === 'canvas'"
            ref="mindMapCanvas"
            :mindmap="currentMindMap"
            :layout="currentLayout"
            :selected-node="selectedNode"
            @select-node="selectNode"
            @update-node="updateNode"
            @add-node="handleAddNode"
            @delete-node="handleDeleteNode"
            @canvas-click="handleCanvasClick"
          />

          <!-- Cytoscape图形模式 -->
          <CytoscapeMindMap
            v-else-if="viewMode === 'cytoscape'"
            ref="cytoscapeMindMap"
            :mindmap="currentMindMap"
            :selected-node="selectedNode"
            @select-node="selectNode"
            @update-node="updateNode"
            @add-node="handleAddNode"
            @delete-node="handleDeleteNode"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑思维导图对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingMindMap ? '编辑思维导图' : '创建思维导图'"
      width="500px"
    >
      <el-form :model="mindMapForm" label-width="80px">
        <el-form-item label="标题" required>
          <el-input v-model="mindMapForm.title" placeholder="请输入思维导图标题" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="mindMapForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入思维导图描述"
          />
        </el-form-item>
        <el-form-item label="模板">
          <el-select v-model="mindMapForm.template" placeholder="选择模板">
            <el-option label="空白模板" value="blank" />
            <el-option label="学习计划" value="study-plan" />
            <el-option label="知识结构" value="knowledge" />
            <el-option label="项目规划" value="project" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmCreateMindMap">
          {{ editingMindMap ? '保存' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 节点编辑对话框 -->
    <el-dialog
      v-model="showNodeDialog"
      title="编辑节点"
      width="400px"
    >
      <el-form :model="nodeForm" label-width="60px">
        <el-form-item label="文本" required>
          <el-input v-model="nodeForm.text" placeholder="请输入节点文本" />
        </el-form-item>
        <el-form-item label="颜色">
          <el-color-picker v-model="nodeForm.color" />
        </el-form-item>
        <el-form-item label="图标">
          <el-select v-model="nodeForm.icon" placeholder="选择图标">
            <el-option label="无图标" value="" />
            <el-option label="文档" value="document" />
            <el-option label="文件夹" value="folder" />
            <el-option label="星星" value="star" />
            <el-option label="灯泡" value="lightbulb" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="nodeForm.notes"
            type="textarea"
            :rows="2"
            placeholder="节点备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showNodeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmEditNode">保存</el-button>
      </template>
    </el-dialog>

    <!-- 任务关联对话框 -->
    <TaskLinkManager
      v-model="showTaskLinkDialog"
      :node="selectedNode"
      @link-task="handleLinkTask"
      @unlink-task="handleUnlinkTask"
      @create-task="handleCreateTask"
    />

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="思维导图设置"
      width="600px"
    >
      <MindMapGlobalSettings
        @update-settings="updateGlobalSettings"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Calendar,
    Check,
    Close,
    Delete,
    Download,
    Edit,
    Link,
    Plus,
    Refresh,
    Search,
    Setting,
    Share,
    Upload,
    User,
    ZoomIn,
    ZoomOut
  } from '@element-plus/icons-vue'
  import MindMapCanvas from '@/components/mindmap/MindMapCanvas.vue'
  import CytoscapeMindMap from '@/components/mindmap/CytoscapeMindMap.vue'
  import MindMapNodeTree from '@/components/mindmap/MindMapNodeTree.vue'
  import MindMapStylePanel from '@/components/mindmap/MindMapStylePanel.vue'
  import MindMapSettings from '@/components/mindmap/MindMapSettings.vue'
  import MindMapGlobalSettings from '@/components/mindmap/MindMapGlobalSettings.vue'
  import TaskLinkManager from '@/components/mindmap/TaskLinkManager.vue'
  import type { MindMap, MindMapNode } from '@/types/mindmap'
  import { useTaskStore } from '@/stores/task'
  import { debounce, performanceMonitor } from '@/utils/performance'
  import dayjs from 'dayjs'

  // 响应式数据
  const mindMaps = ref<MindMap[]>([])
  const currentMindMap = ref<MindMap | null>(null)
  const selectedNode = ref<MindMapNode | null>(null)
  const currentLayout = ref<'tree' | 'radial' | 'force' | 'hierarchical'>('tree')
  const viewMode = ref<'canvas' | 'cytoscape'>('canvas')
  const activeTab = ref('nodes')
  const searchText = ref('')
  const sortBy = ref('updated')

  // 对话框状态
  const showCreateDialog = ref(false)
  const showNodeDialog = ref(false)
  const showSettings = ref(false)
  const showTaskLinkDialog = ref(false)
  const editingMindMap = ref<MindMap | null>(null)

  // Stores
  const taskStore = useTaskStore()

  // 表单数据
  const mindMapForm = ref({
    title: '',
    description: '',
    template: 'blank'
  })

  const nodeForm = ref({
    text: '',
    color: '#409EFF',
    icon: '',
    notes: ''
  })

  // 组件引用
  const mindMapCanvas = ref<InstanceType<typeof MindMapCanvas>>()
  const cytoscapeMindMap = ref<InstanceType<typeof CytoscapeMindMap>>()

  // 计算属性
  const filteredMindMaps = computed(() => {
    let filtered = mindMaps.value

    if (searchText.value) {
      filtered = filtered.filter(mindmap =>
        mindmap.title.toLowerCase().includes(searchText.value.toLowerCase()) ||
        mindmap.description.toLowerCase().includes(searchText.value.toLowerCase())
      )
    }

    return filtered.sort((a, b) => {
      switch (sortBy.value) {
        case 'updated':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'name':
          return a.title.localeCompare(b.title)
        default:
          return 0
      }
    })
  })

  // 方法
  const formatDate = (dateStr: string): string => {
    return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
  }

  const createMindMap = () => {
    mindMapForm.value = {
      title: '',
      description: '',
      template: 'blank'
    }
    editingMindMap.value = null
    showCreateDialog.value = true
  }

  const confirmCreateMindMap = () => {
    if (!mindMapForm.value.title) {
      ElMessage.warning('请输入思维导图标题')
      return
    }

    const newMindMap: MindMap = {
      id: Date.now().toString(),
      title: mindMapForm.value.title,
      description: mindMapForm.value.description,
      nodes: [],
      edges: [],
      layout: 'tree',
      nodeCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // 根据模板创建初始节点
    if (mindMapForm.value.template !== 'blank') {
      newMindMap.nodes = createTemplateNodes(mindMapForm.value.template)
      newMindMap.nodeCount = newMindMap.nodes.length
    }

    mindMaps.value.push(newMindMap)
    showCreateDialog.value = false
    openMindMap(newMindMap)
    ElMessage.success('思维导图创建成功')
  }

  const createTemplateNodes = (template: string): MindMapNode[] => {
    // 根据不同模板创建初始节点结构
    switch (template) {
      case 'study-plan':
        return [
          { id: '1', text: '学习计划', x: 0, y: 0, color: '#409EFF', level: 0 },
          { id: '2', text: '目标设定', x: 100, y: -50, color: '#67C23A', level: 1, parentId: '1' },
          { id: '3', text: '时间安排', x: 100, y: 0, color: '#E6A23C', level: 1, parentId: '1' },
          { id: '4', text: '学习方法', x: 100, y: 50, color: '#F56C6C', level: 1, parentId: '1' }
        ]
      case 'knowledge':
        return [
          { id: '1', text: '知识体系', x: 0, y: 0, color: '#409EFF', level: 0 },
          { id: '2', text: '基础概念', x: 100, y: -50, color: '#67C23A', level: 1, parentId: '1' },
          { id: '3', text: '核心原理', x: 100, y: 0, color: '#E6A23C', level: 1, parentId: '1' },
          { id: '4', text: '应用实践', x: 100, y: 50, color: '#F56C6C', level: 1, parentId: '1' }
        ]
      default:
        return [
          { id: '1', text: '中心主题', x: 0, y: 0, color: '#409EFF', level: 0 }
        ]
    }
  }

  const openMindMap = (mindmap: MindMap) => {
    performanceMonitor.mark('mindmap-open-start')

    currentMindMap.value = mindmap
    selectedNode.value = null

    // 延迟测量以确保渲染完成
    setTimeout(() => {
      performanceMonitor.mark('mindmap-open-end')
      const duration = performanceMonitor.measure('mindmap-open', 'mindmap-open-start', 'mindmap-open-end')
      console.log(`思维导图打开耗时: ${duration.toFixed(2)}ms`)
    }, 100)
  }

  const closeMindMap = () => {
    currentMindMap.value = null
    selectedNode.value = null
  }

  const selectNode = (node: MindMapNode) => {
    selectedNode.value = node
  }

  const addNode = () => {
    if (!currentMindMap.value) {return}

    const parentNode = selectedNode.value || currentMindMap.value.nodes[0]
    const newNode: MindMapNode = {
      id: Date.now().toString(),
      text: '新节点',
      x: parentNode ? parentNode.x + 100 : 0,
      y: parentNode ? parentNode.y + 50 : 0,
      color: '#409EFF',
      level: parentNode ? parentNode.level + 1 : 0,
      parentId: parentNode?.id
    }

    currentMindMap.value.nodes.push(newNode)
    currentMindMap.value.nodeCount = currentMindMap.value.nodes.length
    selectedNode.value = newNode
  }

  const editNode = () => {
    if (!selectedNode.value) {return}

    nodeForm.value = {
      text: selectedNode.value.text,
      color: selectedNode.value.color,
      icon: selectedNode.value.icon || '',
      notes: selectedNode.value.notes || ''
    }
    showNodeDialog.value = true
  }

  const confirmEditNode = () => {
    if (!selectedNode.value) {return}

    selectedNode.value.text = nodeForm.value.text
    selectedNode.value.color = nodeForm.value.color
    selectedNode.value.icon = nodeForm.value.icon || undefined
    selectedNode.value.notes = nodeForm.value.notes || undefined

    showNodeDialog.value = false
    ElMessage.success('节点更新成功')
  }

  const deleteNode = () => {
    if (!selectedNode.value || !currentMindMap.value) {return}

    ElMessageBox.confirm(
      '确定要删除这个节点吗？子节点也会被删除。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      if (!selectedNode.value || !currentMindMap.value) {return}

      // 删除节点及其子节点
      const nodeIdsToDelete = [selectedNode.value.id]
      const findChildNodes = (parentId: string) => {
        currentMindMap.value!.nodes.forEach(node => {
          if (node.parentId === parentId) {
            nodeIdsToDelete.push(node.id)
            findChildNodes(node.id)
          }
        })
      }
      findChildNodes(selectedNode.value.id)

      currentMindMap.value.nodes = currentMindMap.value.nodes.filter(
        node => !nodeIdsToDelete.includes(node.id)
      )
      currentMindMap.value.nodeCount = currentMindMap.value.nodes.length
      selectedNode.value = null
      ElMessage.success('节点删除成功')
    }).catch(() => {
      // 用户取消删除
    })
  }

  const handleAddNode = (parentNode: MindMapNode) => {
    selectedNode.value = parentNode
    addNode()
  }

  const handleDeleteNode = (node: MindMapNode) => {
    selectedNode.value = node
    deleteNode()
  }

  const handleCanvasClick = () => {
    selectedNode.value = null
  }

  const updateNode = (node: MindMapNode) => {
    if (!currentMindMap.value) {return}

    const index = currentMindMap.value.nodes.findIndex(n => n.id === node.id)
    if (index !== -1) {
      currentMindMap.value.nodes[index] = node
    }
  }

  // 使用防抖优化节点样式更新
  const updateNodeStyle = debounce((style: any) => {
    if (!selectedNode.value) {return}

    performanceMonitor.mark('node-style-update-start')
    Object.assign(selectedNode.value, style)

    setTimeout(() => {
      performanceMonitor.mark('node-style-update-end')
      const duration = performanceMonitor.measure('node-style-update', 'node-style-update-start', 'node-style-update-end')
      console.log(`节点样式更新耗时: ${duration.toFixed(2)}ms`)
    }, 50)
  }, 300)

  // 任务关联功能
  const linkTask = () => {
    if (!selectedNode.value) {return}
    showTaskLinkDialog.value = true
  }

  const handleLinkTask = (data: {
    nodeId: string
    taskId: string
    config: any
  }) => {
    if (!currentMindMap.value) {return}

    const node = currentMindMap.value.nodes.find(n => n.id === data.nodeId)
    if (!node) {return}

    // 更新节点的任务关联信息
    node.taskId = data.taskId
    node.color = data.config.nodeColor
    node.size = data.config.nodeSize
    node.shape = data.config.nodeShape

    // 获取任务信息并同步状态
    const task = taskStore.tasks.find(t => t.id === data.taskId)
    if (task) {
      node.taskStatus = task.status
      node.taskPriority = task.priority
      node.taskProgress = calculateTaskProgress(task)

      // 如果启用状态同步，更新复习状态
      if (data.config.syncStatus) {
        updateNodeReviewStatus(node, task)
      }
    }

    ElMessage.success('任务关联成功')
  }

  const handleUnlinkTask = (nodeId: string) => {
    if (!currentMindMap.value) {return}

    const node = currentMindMap.value.nodes.find(n => n.id === nodeId)
    if (!node) {return}

    // 清除任务关联信息
    node.taskId = undefined
    node.taskStatus = undefined
    node.taskPriority = undefined
    node.taskProgress = undefined
    node.reviewStatus = undefined
    node.nextReviewTime = undefined

    ElMessage.success('任务关联已解除')
  }

  const calculateTaskProgress = (task: any) => {
    // 根据任务状态计算进度
    switch (task.status) {
      case 'completed':
        return 100
      case 'in-progress':
        return 50 // 可以根据实际情况调整
      case 'pending':
        return 0
      default:
        return 0
    }
  }

  const updateNodeReviewStatus = (node: MindMapNode, task: any) => {
    // 这里可以集成复习系统的逻辑
    // 暂时使用简单的状态映射
    if (task.status === 'completed') {
      node.reviewStatus = 'completed'
    } else if (task.status === 'in-progress') {
      node.reviewStatus = 'in-progress'
    } else {
      node.reviewStatus = 'pending'
    }
  }

  const handleStartReview = (taskId: string) => {
    // 跳转到复习页面或启动复习流程
    ElMessage.info('启动复习功能')
    // 这里可以集成到复习系统
  }

  const handleViewTask = (taskId: string) => {
    // 跳转到任务详情页面
    ElMessage.info('查看任务详情')
    // 这里可以跳转到任务管理页面
  }

  const handleCreateTask = (node: MindMapNode) => {
    // 基于节点信息创建新任务
    const newTask = {
      title: node.label || '新任务',
      description: `基于思维导图节点"${node.label}"创建的任务`,
      subject: '其他',
      difficulty: 3,
      priority: 3,
      estimatedTime: 30,
      status: 'pending'
    }

    // 这里应该调用任务创建API
    ElMessage.success('任务创建成功')
  }

  const updateMindMapSettings = (settings: any) => {
    if (!currentMindMap.value) {return}

    Object.assign(currentMindMap.value, settings)
  }

  const updateGlobalSettings = (_settings: any) => {
    // 更新全局设置
    ElMessage.success('设置已保存')
  }

  const changeLayout = (layout: string) => {
    currentLayout.value = layout as any
  }

  const zoomIn = () => {
    if (viewMode.value === 'cytoscape' && cytoscapeMindMap.value) {
      // Cytoscape模式下的放大
      const cy = (cytoscapeMindMap.value as any).cy?.value
      if (cy) {
        cy.zoom(cy.zoom() * 1.2)
      }
    } else {
      // 画布模式下的放大
      ElMessage.info('画布放大功能开发中...')
    }
  }

  const zoomOut = () => {
    if (viewMode.value === 'cytoscape' && cytoscapeMindMap.value) {
      // Cytoscape模式下的缩小
      const cy = (cytoscapeMindMap.value as any).cy?.value
      if (cy) {
        cy.zoom(cy.zoom() / 1.2)
      }
    } else {
      // 画布模式下的缩小
      ElMessage.info('画布缩小功能开发中...')
    }
  }

  const resetZoom = () => {
    if (viewMode.value === 'cytoscape' && cytoscapeMindMap.value) {
      // Cytoscape模式下的重置视图
      const cy = (cytoscapeMindMap.value as any).cy?.value
      if (cy) {
        cy.fit()
        cy.center()
      }
    } else {
      // 画布模式下的重置缩放
      ElMessage.info('画布重置缩放功能开发中...')
    }
  }

  const saveMindMap = () => {
    if (!currentMindMap.value) {return}

    currentMindMap.value.updatedAt = new Date().toISOString()
    ElMessage.success('思维导图已保存')
  }

  const exportMindMap = () => {
    ElMessage.info('导出功能开发中...')
  }

  const shareMindMap = () => {
    ElMessage.info('分享功能开发中...')
  }

  const importMindMap = () => {
    ElMessage.info('导入功能开发中...')
  }

  const editMindMapInfo = (mindmap: MindMap) => {
    mindMapForm.value = {
      title: mindmap.title,
      description: mindmap.description,
      template: 'blank'
    }
    editingMindMap.value = mindmap
    showCreateDialog.value = true
  }

  const deleteMindMap = (mindmap: MindMap) => {
    ElMessageBox.confirm(
      `确定要删除思维导图"${mindmap.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      const index = mindMaps.value.findIndex(m => m.id === mindmap.id)
      if (index !== -1) {
        mindMaps.value.splice(index, 1)
        ElMessage.success('思维导图删除成功')
      }
    }).catch(() => {
      // 用户取消删除
    })
  }

  // 生命周期
  onMounted(() => {
    // 加载思维导图数据
    loadMindMaps()
  })

  const loadMindMaps = () => {
    // 模拟加载数据
    mindMaps.value = [
      {
        id: '1',
        title: '前端学习路线',
        description: '完整的前端开发学习计划',
        nodes: [],
        edges: [],
        layout: 'tree',
        nodeCount: 12,
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-20T15:30:00Z'
      },
      {
        id: '2',
        title: 'Vue.js 知识体系',
        description: 'Vue.js 框架的核心概念和应用',
        nodes: [],
        edges: [],
        layout: 'radial',
        nodeCount: 8,
        createdAt: '2024-01-10T09:00:00Z',
        updatedAt: '2024-01-18T14:20:00Z'
      }
    ]
  }
</script>

<style scoped>
  .mindmap-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .header-left h2 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-primary);
  }

  .header-left p {
    margin: 0;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: var(--el-fill-color-light);
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .toolbar-right {
    display: flex;
    gap: 8px;
  }

  .mindmap-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .mindmap-list {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .list-header h3 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .list-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .mindmap-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .mindmap-card {
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--el-bg-color);
  }

  .mindmap-card:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .card-preview {
    height: 120px;
    background: var(--el-fill-color-light);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .preview-placeholder {
    font-size: 48px;
    color: var(--el-text-color-placeholder);
  }

  .card-content {
    padding: 16px;
  }

  .card-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .card-description {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);
    line-height: 1.5;
  }

  .card-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--el-text-color-placeholder);
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .mindmap-editor {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .editor-sidebar {
    width: 300px;
    background: var(--el-bg-color);
    border-right: 1px solid var(--el-border-color-light);
    display: flex;
    flex-direction: column;
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .sidebar-header h4 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .sidebar-tabs {
    flex: 1;
    overflow: hidden;
  }

  .sidebar-tabs :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    overflow-y: auto;
  }

  .nodes-panel,
  .style-panel,
  .settings-panel {
    padding: 16px;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .editor-canvas {
    flex: 1;
    background: var(--el-fill-color-lighter);
    position: relative;
    overflow: hidden;
  }

  @media (max-width: 768px) {
    .mindmap-page {
      height: 100vh;
    }

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .header-actions {
      justify-content: center;
    }

    .toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
      justify-content: center;
    }

    .list-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .list-controls {
      flex-direction: column;
    }

    .mindmap-grid {
      grid-template-columns: 1fr;
    }

    .mindmap-editor {
      flex-direction: column;
    }

    .editor-sidebar {
      width: 100%;
      height: 200px;
      border-right: none;
      border-bottom: 1px solid var(--el-border-color-light);
    }

    .sidebar-tabs :deep(.el-tabs__content) {
      height: calc(100% - 40px);
    }
  }
</style>
