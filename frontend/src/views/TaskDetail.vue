<template>
  <div class="task-detail-page">
    <div class="page-header">
      <div class="header-left">
        <el-button link class="back-btn" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h2>任务详情</h2>
      </div>
      <div v-if="task" class="header-actions">
        <el-button :icon="Edit" @click="editTask">编辑</el-button>
        <el-button type="primary" :icon="VideoPlay" @click="startReview">
          开始复习
        </el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-loading :loading="true" element-loading-text="加载中..." />
    </div>

    <div v-else-if="task" class="task-detail">
      <!-- 基本信息卡片 -->
      <el-card class="basic-info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(task.status)">{{ getStatusText(task.status) }}</el-tag>
          </div>
        </template>

        <div class="task-content">
          <h3 class="task-title">{{ task.title }}</h3>
          <p class="task-description">{{ task.content }}</p>

          <div class="task-meta">
            <div class="meta-item">
              <span class="meta-label">学科:</span>
              <el-tag>{{ getSubjectLabel(task.subject) }}</el-tag>
            </div>
            <div class="meta-item">
              <span class="meta-label">预估时间:</span>
              <span class="meta-value">{{ task.estimatedTime }}分钟</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">优先级:</span>
              <el-rate v-model="task.priority" disabled size="small" />
            </div>
            <div class="meta-item">
              <span class="meta-label">难度:</span>
              <el-rate v-model="task.difficulty" disabled size="small" />
            </div>
            <div class="meta-item">
              <span class="meta-label">创建时间:</span>
              <span class="meta-value">{{ formatDate(task.createdAt) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">下次复习:</span>
              <span class="meta-value">{{ formatDate(task.nextReviewTime) }}</span>
            </div>
          </div>

          <div v-if="task.tags && task.tags.length > 0" class="task-tags">
            <span class="tags-label">标签:</span>
            <el-tag
              v-for="tag in task.tags"
              :key="tag"
              size="small"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 复习计划卡片 -->
      <el-card class="review-plan-card">
        <template #header>
          <div class="card-header">
            <span>复习计划</span>
            <el-button size="small" :icon="Refresh" @click="refreshReviewPlan">
              刷新
            </el-button>
          </div>
        </template>

        <div v-if="reviewPlan" class="review-plan-content">
          <ReviewTimeline
            :reviews="reviewPlan.reviews"
            :task="task"
            @start-review="handleStartReview"
            @skip-review="handleSkipReview"
          />
        </div>
        <div v-else class="no-review-plan">
          <el-empty description="暂无复习计划">
            <el-button type="primary" @click="createReviewPlan">
              创建复习计划
            </el-button>
          </el-empty>
        </div>
      </el-card>

      <!-- 复习历史卡片 -->
      <el-card class="review-history-card">
        <template #header>
          <div class="card-header">
            <span>复习历史</span>
            <el-button size="small" :icon="Download" @click="exportHistory">
              导出
            </el-button>
          </div>
        </template>

        <div class="review-history-content">
          <ReviewHistory
            :task-id="task.id"
            :reviews="completedReviews"
          />
        </div>
      </el-card>

      <!-- 学习统计卡片 -->
      <el-card class="learning-stats-card">
        <template #header>
          <span>学习统计</span>
        </template>

        <div class="learning-stats-content">
          <LearningStats
            :task="task"
            :review-plan="reviewPlan"
          />
        </div>
      </el-card>
    </div>

    <div v-else class="error-state">
      <el-empty description="任务不存在">
        <el-button type="primary" @click="goBack">返回任务列表</el-button>
      </el-empty>
    </div>

    <!-- 复习执行对话框 -->
    <ReviewExecution
      v-model="showReviewDialog"
      :review="currentReview || undefined"
      :task="task || undefined"
      @complete="handleReviewComplete"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    ArrowLeft,
    Download,
    Edit,
    Refresh,
    VideoPlay
  } from '@element-plus/icons-vue'
  import { useTaskStore } from '@/stores/task'
  import { useReviewStore } from '@/stores/review'
  import { subjectOptions } from '@/mock/taskData'
  import ReviewTimeline from '@/components/review/ReviewTimeline.vue'
  import ReviewHistory from '@/components/review/ReviewHistory.vue'
  import ReviewExecution from '@/components/review/ReviewExecution.vue'
  import LearningStats from '@/components/task/LearningStats.vue'
  import type { ReviewPlan, ReviewRecord, Task } from '@/types'
  import dayjs from 'dayjs'

  const route = useRoute()
  const router = useRouter()
  const taskStore = useTaskStore()
  const reviewStore = useReviewStore()

  // 响应式数据
  const task = ref<Task | null>(null)
  const loading = ref(false)
  const reviewPlan = ref<ReviewPlan | null>(null)
  const showReviewDialog = ref(false)
  const currentReview = ref<ReviewRecord | null>(null)

  // 计算属性
  const completedReviews = computed(() => {
    if (!reviewPlan.value) {return []}
    return reviewPlan.value.reviews.filter(review => review.status === 'completed')
  })

  // 方法
  const getSubjectLabel = (subject: string) => {
    return subjectOptions.find((s) => s.value === subject)?.label || subject
  }

  const getStatusType = (status: string): string => {
    switch (status) {
      case 'completed': return 'success'
      case 'paused': return 'warning'
      case 'active': return 'primary'
      default: return 'info'
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed': return '已完成'
      case 'paused': return '已暂停'
      case 'active': return '进行中'
      default: return '未知'
    }
  }

  const formatDate = (dateStr: string): string => {
    return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
  }

  const goBack = () => {
    router.back()
  }

  const editTask = () => {
    if (task.value) {
      router.push(`/tasks/${task.value.id}/edit`)
    }
  }

  const startReview = () => {
    if (!reviewPlan.value) {
      ElMessage.warning('请先创建复习计划')
      return
    }

    const nextReview = reviewPlan.value.reviews.find(r => r.status === 'scheduled')
    if (nextReview) {
      currentReview.value = nextReview
      showReviewDialog.value = true
    } else {
      ElMessage.info('暂无待复习的内容')
    }
  }

  const refreshReviewPlan = async () => {
    if (task.value) {
      try {
        const plan = await reviewStore.getReviewPlan(task.value.id)
        reviewPlan.value = plan || null
        ElMessage.success('复习计划已刷新')
      } catch (error) {
        ElMessage.error('刷新复习计划失败')
        reviewPlan.value = null
      }
    }
  }

  const createReviewPlan = async () => {
    if (!task.value) {return}

    try {
      await ElMessageBox.confirm(
        '确定要为此任务创建复习计划吗？',
        '创建复习计划',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      // TODO: 实现创建复习计划逻辑
      ElMessage.info('创建复习计划功能开发中...')
    } catch {
      // 用户取消
    }
  }

  const handleStartReview = (review: ReviewRecord) => {
    currentReview.value = review
    showReviewDialog.value = true
  }

  const handleSkipReview = async (review: ReviewRecord) => {
    try {
      await ElMessageBox.confirm(
        '确定要跳过这次复习吗？',
        '确认跳过',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const success = await reviewStore.skipReview(review.taskId, review.intervalId)
      if (success) {
        ElMessage.success('已跳过复习')
        await refreshReviewPlan()
      }
    } catch {
      // 用户取消
    }
  }

  const handleReviewComplete = async (data: {
    rating: number
    notes: string
    duration: number
  }) => {
    if (!currentReview.value) {return}

    const success = await reviewStore.executeReview(
      currentReview.value.taskId,
      currentReview.value.intervalId,
      data.rating,
      data.notes,
      data.duration
    )

    if (success) {
      currentReview.value = null
      await refreshReviewPlan()
    }
  }

  const exportHistory = () => {
    // TODO: 实现导出复习历史功能
    ElMessage.info('导出功能开发中...')
  }

  // 生命周期
  onMounted(async () => {
    const taskId = route.params.id as string
    if (taskId) {
      loading.value = true
      try {
        task.value = await taskStore.getTask(taskId)
        if (task.value) {
          // 加载复习计划
          try {
            const plan = await reviewStore.getReviewPlan(taskId)
            reviewPlan.value = plan || null
          } catch (error) {
            console.log('暂无复习计划')
            reviewPlan.value = null
          }
        }
      } catch (error) {
        console.error('获取任务详情失败:', error)
        ElMessage.error('获取任务详情失败')
      } finally {
        loading.value = false
      }
    }
  })
</script>

<style scoped>
  .task-detail-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-left h2 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .back-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--el-text-color-secondary);
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }

  .loading-container {
    height: 400px;
    position: relative;
  }

  .task-detail {
    display: grid;
    gap: 24px;
  }

  .basic-info-card,
  .review-plan-card,
  .review-history-card,
  .learning-stats-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .task-content {
    padding: 4px 0;
  }

  .task-title {
    margin: 0 0 12px 0;
    color: var(--el-text-color-primary);
    font-size: 24px;
    font-weight: 600;
  }

  .task-description {
    margin: 0 0 20px 0;
    color: var(--el-text-color-regular);
    line-height: 1.6;
    font-size: 16px;
  }

  .task-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .meta-label {
    font-weight: 500;
    color: var(--el-text-color-secondary);
    min-width: 80px;
  }

  .meta-value {
    color: var(--el-text-color-primary);
  }

  .task-tags {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .tags-label {
    font-weight: 500;
    color: var(--el-text-color-secondary);
  }

  .tag-item {
    margin: 0;
  }

  .review-plan-content,
  .review-history-content,
  .learning-stats-content {
    padding: 4px 0;
  }

  .no-review-plan {
    text-align: center;
    padding: 40px 0;
  }

  .error-state {
    text-align: center;
    padding: 60px 0;
  }

  @media (max-width: 768px) {
    .task-detail-page {
      padding: 12px;
    }

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .header-left {
      justify-content: space-between;
    }

    .header-actions {
      justify-content: center;
    }

    .task-meta {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .meta-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .meta-label {
      min-width: auto;
    }

    .task-tags {
      flex-direction: column;
      align-items: flex-start;
    }
  }
</style>
