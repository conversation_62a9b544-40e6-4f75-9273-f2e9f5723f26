/**
 * 复习提醒通知服务
 * 实现浏览器通知API集成、应用内消息系统、免打扰时间设置、提醒频率自定义
 */

export interface NotificationOptions {
  title: string
  body: string
  icon?: string
  tag?: string
  requireInteraction?: boolean
  actions?: NotificationAction[]
}

export interface InAppMessage {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  duration?: number // 显示时长（毫秒），0表示不自动关闭
  actions?: Array<{
    text: string
    action: () => void
  }>
  createdAt: string
}

export interface ReminderSettings {
  enabled: boolean
  browserNotifications: boolean
  inAppMessages: boolean
  sound: boolean
  quietHours: {
    enabled: boolean
    start: string // HH:mm格式
    end: string   // HH:mm格式
  }
  frequency: {
    beforeReview: number // 复习前多少分钟提醒
    overdue: number      // 逾期后多少分钟再次提醒
  }
}

export interface ReviewReminder {
  id: string
  taskId: string
  taskTitle: string
  reviewTime: string
  reminderTime: string
  type: 'upcoming' | 'overdue'
  isActive: boolean
}

class NotificationService {
  private static instance: NotificationService
  private inAppMessages: InAppMessage[] = []
  private messageListeners: Array<(messages: InAppMessage[]) => void> = []
  private activeReminders: Map<string, number> = new Map() // 存储定时器ID
  private settings: ReminderSettings

  private constructor() {
    this.settings = this.loadSettings()
    this.initializeService()
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  /**
   * 初始化通知服务
   */
  private initializeService(): void {
    // 请求浏览器通知权限
    if (this.settings.browserNotifications) {
      this.requestBrowserPermission()
    }

    // 监听页面可见性变化，页面重新可见时检查逾期提醒
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkOverdueReminders()
      }
    })
  }

  /**
   * 请求浏览器通知权限
   */
  public async requestBrowserPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('此浏览器不支持桌面通知')
      return false
    }

    if (Notification.permission === 'granted') {
      return true
    }

    if (Notification.permission === 'denied') {
      return false
    }

    const permission = await Notification.requestPermission()
    return permission === 'granted'
  }

  /**
   * 发送浏览器通知
   */
  public async sendBrowserNotification(options: NotificationOptions): Promise<void> {
    if (!this.settings.browserNotifications || !this.canSendNotification()) {
      return
    }

    const hasPermission = await this.requestBrowserPermission()
    if (!hasPermission) {
      console.warn('没有浏览器通知权限')
      return
    }

    const notification = new Notification(options.title, {
      body: options.body,
      icon: options.icon || '/favicon.ico',
      tag: options.tag,
      requireInteraction: options.requireInteraction || false,
      badge: '/favicon.ico'
    })

    // 点击通知时聚焦到窗口
    notification.onclick = () => {
      window.focus()
      notification.close()
    }

    // 自动关闭通知（5秒后）
    setTimeout(() => {
      notification.close()
    }, 5000)
  }

  /**
   * 发送应用内消息
   */
  public sendInAppMessage(message: Omit<InAppMessage, 'id' | 'createdAt'>): string {
    if (!this.settings.inAppMessages) {
      return ''
    }

    const inAppMessage: InAppMessage = {
      ...message,
      id: this.generateId(),
      createdAt: new Date().toISOString()
    }

    this.inAppMessages.unshift(inAppMessage)
    this.notifyMessageListeners()

    // 自动移除消息
    if (message.duration !== 0) {
      const duration = message.duration || 5000
      setTimeout(() => {
        this.removeInAppMessage(inAppMessage.id)
      }, duration)
    }

    return inAppMessage.id
  }

  /**
   * 移除应用内消息
   */
  public removeInAppMessage(messageId: string): void {
    const index = this.inAppMessages.findIndex(msg => msg.id === messageId)
    if (index > -1) {
      this.inAppMessages.splice(index, 1)
      this.notifyMessageListeners()
    }
  }

  /**
   * 获取所有应用内消息
   */
  public getInAppMessages(): InAppMessage[] {
    return [...this.inAppMessages]
  }

  /**
   * 订阅应用内消息变化
   */
  public subscribeToMessages(listener: (messages: InAppMessage[]) => void): () => void {
    this.messageListeners.push(listener)
    
    // 返回取消订阅函数
    return () => {
      const index = this.messageListeners.indexOf(listener)
      if (index > -1) {
        this.messageListeners.splice(index, 1)
      }
    }
  }

  /**
   * 安排复习提醒
   */
  public scheduleReviewReminder(reminder: ReviewReminder): void {
    if (!this.settings.enabled) {
      return
    }

    // 取消已存在的提醒
    this.cancelReminder(reminder.id)

    const reminderTime = new Date(reminder.reminderTime).getTime()
    const now = Date.now()
    const delay = reminderTime - now

    if (delay <= 0) {
      // 立即发送提醒
      this.sendReviewReminder(reminder)
      return
    }

    // 设置定时器
    const timerId = window.setTimeout(() => {
      this.sendReviewReminder(reminder)
      this.activeReminders.delete(reminder.id)
    }, delay)

    this.activeReminders.set(reminder.id, timerId)
  }

  /**
   * 取消提醒
   */
  public cancelReminder(reminderId: string): void {
    const timerId = this.activeReminders.get(reminderId)
    if (timerId) {
      clearTimeout(timerId)
      this.activeReminders.delete(reminderId)
    }
  }

  /**
   * 发送复习提醒
   */
  private sendReviewReminder(reminder: ReviewReminder): void {
    const isOverdue = reminder.type === 'overdue'
    const title = isOverdue ? '复习逾期提醒' : '复习时间到了'
    const body = `${reminder.taskTitle} ${isOverdue ? '已逾期，请尽快复习' : '该复习了'}`

    // 发送浏览器通知
    this.sendBrowserNotification({
      title,
      body,
      tag: `review-${reminder.taskId}`,
      requireInteraction: isOverdue,
      actions: [
        { action: 'start-review', title: '开始复习' },
        { action: 'snooze', title: '稍后提醒' }
      ]
    })

    // 发送应用内消息
    this.sendInAppMessage({
      type: isOverdue ? 'warning' : 'info',
      title,
      message: body,
      duration: isOverdue ? 0 : 8000, // 逾期消息不自动关闭
      actions: [
        {
          text: '开始复习',
          action: () => {
            // 跳转到复习页面
            window.location.href = `/review?task=${reminder.taskId}`
          }
        },
        {
          text: '稍后提醒',
          action: () => {
            // 10分钟后再次提醒
            const newReminderTime = new Date(Date.now() + 10 * 60 * 1000).toISOString()
            this.scheduleReviewReminder({
              ...reminder,
              reminderTime: newReminderTime
            })
          }
        }
      ]
    })

    // 播放提醒音效
    if (this.settings.sound) {
      this.playNotificationSound()
    }
  }

  /**
   * 检查是否可以发送通知（考虑免打扰时间）
   */
  private canSendNotification(): boolean {
    if (!this.settings.quietHours.enabled) {
      return true
    }

    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    const { start, end } = this.settings.quietHours

    // 简单的时间比较（不考虑跨天情况）
    if (start <= end) {
      return currentTime < start || currentTime > end
    } else {
      // 跨天情况
      return currentTime > end && currentTime < start
    }
  }

  /**
   * 检查逾期提醒
   */
  private checkOverdueReminders(): void {
    // 这里应该从复习计划数据中检查逾期的复习任务
    // 暂时留空，等待与复习数据集成
  }

  /**
   * 播放通知音效
   */
  private playNotificationSound(): void {
    try {
      const audio = new Audio('/notification.mp3')
      audio.volume = 0.5
      audio.play().catch(err => {
        console.warn('无法播放通知音效:', err)
      })
    } catch (err) {
      console.warn('通知音效播放失败:', err)
    }
  }

  /**
   * 通知消息监听器
   */
  private notifyMessageListeners(): void {
    this.messageListeners.forEach(listener => {
      listener([...this.inAppMessages])
    })
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 加载设置
   */
  private loadSettings(): ReminderSettings {
    const defaultSettings: ReminderSettings = {
      enabled: true,
      browserNotifications: true,
      inAppMessages: true,
      sound: true,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00'
      },
      frequency: {
        beforeReview: 5,  // 复习前5分钟提醒
        overdue: 30       // 逾期后30分钟再次提醒
      }
    }

    try {
      const saved = localStorage.getItem('reminder-settings')
      return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings
    } catch {
      return defaultSettings
    }
  }

  /**
   * 保存设置
   */
  public saveSettings(settings: Partial<ReminderSettings>): void {
    this.settings = { ...this.settings, ...settings }
    localStorage.setItem('reminder-settings', JSON.stringify(this.settings))
  }

  /**
   * 获取设置
   */
  public getSettings(): ReminderSettings {
    return { ...this.settings }
  }
}

export default NotificationService
