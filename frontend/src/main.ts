import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import { routes } from './router/index'
import './styles/tailwind.css'
import './styles/main.scss'
import './styles/mobile.scss'

// 错误处理
import { ErrorLevel, ErrorType, globalErrorHandler, handleError } from './utils/errorHandler'

const app = createApp(App)

// 配置Vue错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('Vue Error:', error, info)

  handleError(error, ErrorType.CLIENT, ErrorLevel.HIGH)

  // 在开发环境中，继续抛出错误以便调试
  if (import.meta.env.DEV) {
    console.error('Vue Error Details:', {
      error,
      instance,
      info,
      componentName: instance?.$options.name || instance?.$options.__name
    })
  }
}

// 配置Vue警告处理
app.config.warnHandler = (msg, instance, trace) => {
  if (import.meta.env.DEV) {
    console.warn('Vue Warning:', msg, trace)
  }
}

// 创建Pinia实例
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 创建路由
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 配置全局错误处理器
globalErrorHandler.updateConfig({
  enableConsoleLog: import.meta.env.DEV,
  enableRemoteLogging: import.meta.env.PROD,
  enableUserNotification: true,
  remoteLogEndpoint: import.meta.env.VITE_ERROR_LOG_ENDPOINT
})

app.mount('#app')
