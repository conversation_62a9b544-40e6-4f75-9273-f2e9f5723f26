<template>
  <div class="cytoscape-mindmap">
    <div ref="cytoscapeContainer" class="cytoscape-container"></div>
    
    <!-- 工具栏 -->
    <div class="cytoscape-toolbar">
      <div class="toolbar-group">
        <ElButtonGroup>
          <ElButton size="small" :icon="ZoomIn" title="放大" @click="zoomIn" />
          <ElButton size="small" :icon="ZoomOut" title="缩小" @click="zoomOut" />
          <ElButton size="small" :icon="Refresh" title="重置视图" @click="resetView" />
        </ElButtonGroup>
      </div>
      
      <div class="toolbar-group">
        <ElButtonGroup>
          <ElButton size="small" :icon="Plus" title="添加节点" @click="addNode" />
          <ElButton size="small" :icon="Delete" :disabled="!selectedNode" title="删除节点" @click="deleteNode" />
          <ElButton size="small" :icon="Edit" :disabled="!selectedNode" title="编辑节点" @click="editNode" />
        </ElButtonGroup>
      </div>
      
      <div class="toolbar-group">
        <ElSelect v-model="currentLayout" size="small" style="width: 120px" @change="changeLayout">
          <ElOption label="层次布局" value="dagre" />
          <ElOption label="力导向布局" value="cose" />
          <ElOption label="圆形布局" value="circle" />
          <ElOption label="网格布局" value="grid" />
          <ElOption label="同心圆布局" value="concentric" />
        </ElSelect>
      </div>
    </div>
    
    <!-- 节点编辑对话框 -->
    <ElDialog
      v-model="showNodeDialog"
      title="编辑节点"
      width="400px"
    >
      <ElForm :model="nodeForm" label-width="80px">
        <ElFormItem label="标签" required>
          <ElInput v-model="nodeForm.label" placeholder="请输入节点标签" />
        </ElFormItem>
        <ElFormItem label="颜色">
          <ElColorPicker v-model="nodeForm.color" />
        </ElFormItem>
        <ElFormItem label="大小">
          <ElSlider v-model="nodeForm.size" :min="20" :max="100" />
        </ElFormItem>
        <ElFormItem label="形状">
          <ElSelect v-model="nodeForm.shape" style="width: 100%">
            <ElOption label="圆形" value="ellipse" />
            <ElOption label="矩形" value="rectangle" />
            <ElOption label="圆角矩形" value="roundrectangle" />
            <ElOption label="三角形" value="triangle" />
            <ElOption label="菱形" value="diamond" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="备注">
          <ElInput
            v-model="nodeForm.notes"
            type="textarea"
            :rows="3"
            placeholder="节点备注信息"
          />
        </ElFormItem>
      </ElForm>
      
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="showNodeDialog = false">取消</ElButton>
          <ElButton type="primary" @click="saveNode">保存</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import {
  ElButton,
  ElButtonGroup,
  ElColorPicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElSelect,
  ElSlider
} from 'element-plus'
import {
  Delete,
  Edit,
  Plus,
  Refresh,
  ZoomIn,
  ZoomOut
} from '@element-plus/icons-vue'
import cytoscape from 'cytoscape'
import type { Core, EdgeSingular, NodeSingular } from 'cytoscape'
import type { MindMap, MindMapNode } from '@/types'

interface Props {
  mindmap: MindMap | null
  selectedNode?: MindMapNode | null
}

interface Emits {
  (e: 'select-node', node: MindMapNode | null): void
  (e: 'update-node', node: MindMapNode): void
  (e: 'add-node', parentNode: MindMapNode): void
  (e: 'delete-node', node: MindMapNode): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const cytoscapeContainer = ref<HTMLElement>()
const cy = ref<Core>()
const selectedNode = ref<MindMapNode | null>(null)
const currentLayout = ref('dagre')
const showNodeDialog = ref(false)

const nodeForm = ref({
  label: '',
  color: '#409eff',
  size: 40,
  shape: 'ellipse',
  notes: ''
})

// 生命周期
onMounted(() => {
  initCytoscape()
})

onUnmounted(() => {
  if (cy.value) {
    cy.value.destroy()
  }
})

// 监听器
watch(() => props.mindmap, (newMindmap) => {
  if (newMindmap && cy.value) {
    updateCytoscapeData()
  }
}, { deep: true })

watch(() => props.selectedNode, (newNode) => {
  if (cy.value && newNode) {
    const cyNode = cy.value.getElementById(newNode.id)
    if (cyNode.length > 0) {
      cy.value.elements().unselect()
      cyNode.select()
    }
  }
})

// 方法
const initCytoscape = async () => {
  if (!cytoscapeContainer.value) {return}

  // 动态导入布局插件
  try {
    const dagre = await import('cytoscape-dagre')
    const cose = await import('cytoscape-cose-bilkent')
    
    cytoscape.use(dagre.default)
    cytoscape.use(cose.default)
  } catch (error) {
    console.warn('Layout plugins not available:', error)
  }

  cy.value = cytoscape({
    container: cytoscapeContainer.value,
    
    style: [
      {
        selector: 'node',
        style: {
          'background-color': 'data(color)',
          'label': 'data(label)',
          'text-valign': 'center',
          'text-halign': 'center',
          'color': '#fff',
          'text-outline-width': 2,
          'text-outline-color': 'data(color)',
          'font-size': '12px',
          'font-weight': 'bold',
          'width': 'data(size)',
          'height': 'data(size)',
          'shape': 'data(shape)',
          'border-width': 2,
          'border-color': '#fff',
          'transition-property': 'background-color, border-color, width, height',
          'transition-duration': '0.3s'
        }
      },
      {
        selector: 'node:selected',
        style: {
          'border-color': '#409eff',
          'border-width': 4
        }
      },
      {
        selector: 'node:hover',
        style: {
          'background-color': '#67c23a'
        }
      },
      {
        selector: 'edge',
        style: {
          'width': 3,
          'line-color': '#ccc',
          'target-arrow-color': '#ccc',
          'target-arrow-shape': 'triangle',
          'curve-style': 'bezier',
          'transition-property': 'line-color, target-arrow-color',
          'transition-duration': '0.3s'
        }
      },
      {
        selector: 'edge:hover',
        style: {
          'line-color': '#409eff',
          'target-arrow-color': '#409eff'
        }
      },
      // 任务关联节点样式
      {
        selector: 'node[taskId]',
        style: {
          'border-color': '#e6a23c',
          'border-width': 3
        }
      },
      {
        selector: 'node[taskStatus = "completed"]',
        style: {
          'background-color': '#67c23a'
        }
      },
      {
        selector: 'node[taskStatus = "in-progress"]',
        style: {
          'background-color': '#e6a23c'
        }
      },
      {
        selector: 'node[taskStatus = "pending"]',
        style: {
          'background-color': '#909399'
        }
      }
    ],

    layout: {
      name: 'dagre',
      rankDir: 'TB',
      spacingFactor: 1.5
    },

    // 启用平移和缩放
    zoomingEnabled: true,
    userZoomingEnabled: true,
    panningEnabled: true,
    userPanningEnabled: true,
    
    // 设置最小和最大缩放级别
    minZoom: 0.1,
    maxZoom: 3,
    
    // 启用选择
    selectionType: 'single',
    
    // 禁用自动取消选择
    autoungrabify: false
  })

  // 绑定事件
  cy.value.on('tap', 'node', (event) => {
    const node = event.target
    const nodeData = node.data()
    selectedNode.value = convertCyNodeToMindMapNode(nodeData)
    emit('select-node', selectedNode.value)
  })

  cy.value.on('tap', (event) => {
    if (event.target === cy.value) {
      selectedNode.value = null
      emit('select-node', null)
    }
  })

  cy.value.on('cxttap', 'node', (event) => {
    const node = event.target
    const nodeData = node.data()
    selectedNode.value = convertCyNodeToMindMapNode(nodeData)
    editNode()
  })

  // 初始化数据
  if (props.mindmap) {
    updateCytoscapeData()
  }
}

const updateCytoscapeData = () => {
  if (!cy.value || !props.mindmap) {return}

  const elements = convertMindMapToCytoscape(props.mindmap)
  
  cy.value.elements().remove()
  cy.value.add(elements)
  
  // 应用布局
  const layout = cy.value.layout({
    name: currentLayout.value,
    rankDir: currentLayout.value === 'dagre' ? 'TB' : undefined,
    spacingFactor: 1.5,
    animate: true,
    animationDuration: 500
  })
  
  layout.run()
}

const convertMindMapToCytoscape = (mindmap: MindMap) => {
  const elements: any[] = []

  // 添加节点
  mindmap.nodes.forEach(node => {
    elements.push({
      data: {
        id: node.id,
        label: node.label || node.text || '节点',
        color: node.color || '#409eff',
        size: node.size === 'small' ? 30 : node.size === 'large' ? 60 : 40,
        shape: node.shape || 'ellipse',
        taskId: node.taskId,
        taskStatus: node.taskStatus,
        taskPriority: node.taskPriority,
        taskProgress: node.taskProgress
      }
    })
  })

  // 添加边（基于父子关系）
  mindmap.nodes.forEach(node => {
    if (node.parent) {
      elements.push({
        data: {
          id: `${node.parent}-${node.id}`,
          source: node.parent,
          target: node.id
        }
      })
    }
  })

  return elements
}

const convertCyNodeToMindMapNode = (cyNodeData: any): MindMapNode => {
  return {
    id: cyNodeData.id,
    label: cyNodeData.label,
    x: 0, // Cytoscape管理位置
    y: 0,
    color: cyNodeData.color,
    size: cyNodeData.size > 50 ? 'large' : cyNodeData.size < 35 ? 'small' : 'medium',
    shape: cyNodeData.shape,
    taskId: cyNodeData.taskId,
    taskStatus: cyNodeData.taskStatus,
    taskPriority: cyNodeData.taskPriority,
    taskProgress: cyNodeData.taskProgress,
    children: [],
    parent: undefined
  }
}

// 工具栏方法
const zoomIn = () => {
  if (cy.value) {
    cy.value.zoom(cy.value.zoom() * 1.2)
  }
}

const zoomOut = () => {
  if (cy.value) {
    cy.value.zoom(cy.value.zoom() / 1.2)
  }
}

const resetView = () => {
  if (cy.value) {
    cy.value.fit()
    cy.value.center()
  }
}

const changeLayout = () => {
  if (!cy.value) {return}

  const layoutOptions: any = {
    name: currentLayout.value,
    animate: true,
    animationDuration: 500
  }

  // 根据布局类型设置特定选项
  switch (currentLayout.value) {
    case 'dagre':
      layoutOptions.rankDir = 'TB'
      layoutOptions.spacingFactor = 1.5
      break
    case 'cose':
      layoutOptions.idealEdgeLength = 100
      layoutOptions.nodeOverlap = 20
      break
    case 'circle':
      layoutOptions.radius = 200
      break
    case 'grid':
      layoutOptions.spacingFactor = 1.5
      break
    case 'concentric':
      layoutOptions.spacingFactor = 1.5
      break
  }

  const layout = cy.value.layout(layoutOptions)
  layout.run()
}

const addNode = () => {
  if (!selectedNode.value) {
    ElMessage.warning('请先选择一个父节点')
    return
  }
  emit('add-node', selectedNode.value)
}

const deleteNode = () => {
  if (!selectedNode.value) {return}
  emit('delete-node', selectedNode.value)
}

const editNode = () => {
  if (!selectedNode.value) {return}

  nodeForm.value = {
    label: selectedNode.value.label || '',
    color: selectedNode.value.color || '#409eff',
    size: selectedNode.value.size === 'small' ? 30 : selectedNode.value.size === 'large' ? 60 : 40,
    shape: selectedNode.value.shape || 'ellipse',
    notes: ''
  }

  showNodeDialog.value = true
}

const saveNode = () => {
  if (!selectedNode.value) {return}

  const updatedNode: MindMapNode = {
    ...selectedNode.value,
    label: nodeForm.value.label,
    color: nodeForm.value.color,
    size: nodeForm.value.size > 50 ? 'large' : nodeForm.value.size < 35 ? 'small' : 'medium',
    shape: nodeForm.value.shape
  }

  emit('update-node', updatedNode)
  showNodeDialog.value = false
  ElMessage.success('节点更新成功')
}
</script>

<style scoped lang="scss">
.cytoscape-mindmap {
  position: relative;
  width: 100%;
  height: 100%;
  
  .cytoscape-container {
    width: 100%;
    height: 100%;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }
  
  .cytoscape-toolbar {
    position: absolute;
    top: 16px;
    left: 16px;
    display: flex;
    gap: 12px;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(4px);
    z-index: 10;
    
    .toolbar-group {
      display: flex;
      align-items: center;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .cytoscape-mindmap {
    .cytoscape-toolbar {
      flex-direction: column;
      gap: 8px;
      
      .toolbar-group {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
