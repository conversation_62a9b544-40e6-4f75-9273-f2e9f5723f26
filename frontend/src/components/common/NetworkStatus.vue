<template>
  <div class="network-status">
    <!-- 离线提示 -->
    <transition name="slide-down">
      <div v-if="!isOnline" class="offline-banner">
        <div class="offline-content">
          <ElIcon class="offline-icon">
            <WifiOff />
          </ElIcon>
          <span class="offline-text">网络连接已断开，请检查网络设置</span>
          <ElButton size="small" @click="checkConnection">
            重新连接
          </ElButton>
        </div>
      </div>
    </transition>

    <!-- 网络状态指示器 -->
    <div v-if="showIndicator" class="network-indicator" :class="networkClass">
      <ElTooltip :content="networkTooltip" placement="bottom">
        <div class="indicator-dot"></div>
      </ElTooltip>
    </div>

    <!-- 慢网络提示 -->
    <ElNotification
      v-if="showSlowNetworkWarning"
      title="网络连接较慢"
      message="当前网络连接较慢，可能影响使用体验"
      type="warning"
      :duration="5000"
      @close="showSlowNetworkWarning = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { ElButton, ElIcon, ElNotification, ElTooltip } from 'element-plus'
import { WifiOff } from '@element-plus/icons-vue'
import { ErrorLevel, ErrorType, handleNetworkError } from '@/utils/errorHandler'

interface Props {
  showIndicator?: boolean
  autoCheck?: boolean
  checkInterval?: number
}

interface NetworkInfo {
  isOnline: boolean
  effectiveType: string
  downlink: number
  rtt: number
  saveData: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showIndicator: true,
  autoCheck: true,
  checkInterval: 30000 // 30秒
})

// 响应式数据
const isOnline = ref(navigator.onLine)
const networkInfo = ref<NetworkInfo>({
  isOnline: navigator.onLine,
  effectiveType: 'unknown',
  downlink: 0,
  rtt: 0,
  saveData: false
})
const showSlowNetworkWarning = ref(false)
const lastCheckTime = ref(Date.now())
const checkTimer = ref<number>()

// 计算属性
const networkClass = computed(() => {
  if (!isOnline.value) {return 'offline'}
  
  const { effectiveType, rtt } = networkInfo.value
  
  if (effectiveType === '4g' && rtt < 100) {return 'excellent'}
  if (effectiveType === '4g' || (effectiveType === '3g' && rtt < 200)) {return 'good'}
  if (effectiveType === '3g' || rtt < 500) {return 'fair'}
  return 'poor'
})

const networkTooltip = computed(() => {
  if (!isOnline.value) {return '网络已断开'}
  
  const { effectiveType, downlink, rtt } = networkInfo.value
  return `网络类型: ${effectiveType.toUpperCase()}\n下载速度: ${downlink}Mbps\n延迟: ${rtt}ms`
})

// 方法
const updateNetworkInfo = () => {
  isOnline.value = navigator.onLine
  
  // 获取网络连接信息
  const connection = (navigator as any).connection || 
                    (navigator as any).mozConnection || 
                    (navigator as any).webkitConnection

  if (connection) {
    networkInfo.value = {
      isOnline: navigator.onLine,
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false
    }

    // 检查是否为慢网络
    checkSlowNetwork()
  }
}

const checkSlowNetwork = () => {
  const { effectiveType, rtt } = networkInfo.value
  const isSlowNetwork = effectiveType === 'slow-2g' || 
                       effectiveType === '2g' || 
                       rtt > 1000

  if (isSlowNetwork && !showSlowNetworkWarning.value) {
    showSlowNetworkWarning.value = true
  }
}

const checkConnection = async () => {
  try {
    // 尝试发送一个小的网络请求来检查连接
    const response = await fetch('/favicon.ico', {
      method: 'HEAD',
      cache: 'no-cache',
      timeout: 5000
    })
    
    if (response.ok) {
      updateNetworkInfo()
      if (isOnline.value) {
        ElNotification.success({
          title: '网络已连接',
          message: '网络连接已恢复正常',
          duration: 3000
        })
      }
    }
  } catch (error) {
    handleNetworkError(error, 'Connection check failed')
  }
}

const startAutoCheck = () => {
  if (!props.autoCheck) {return}
  
  checkTimer.value = setInterval(() => {
    const now = Date.now()
    if (now - lastCheckTime.value >= props.checkInterval) {
      checkConnection()
      lastCheckTime.value = now
    }
  }, props.checkInterval)
}

const stopAutoCheck = () => {
  if (checkTimer.value) {
    clearInterval(checkTimer.value)
    checkTimer.value = undefined
  }
}

// 事件处理
const handleOnline = () => {
  updateNetworkInfo()
  ElNotification.success({
    title: '网络已连接',
    message: '网络连接已恢复',
    duration: 3000
  })
}

const handleOffline = () => {
  updateNetworkInfo()
  handleNetworkError(
    new Error('Network connection lost'),
    'Network offline'
  )
}

const handleConnectionChange = () => {
  updateNetworkInfo()
}

// 生命周期
onMounted(() => {
  updateNetworkInfo()
  
  // 监听网络状态变化
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 监听网络连接变化
  const connection = (navigator as any).connection || 
                    (navigator as any).mozConnection || 
                    (navigator as any).webkitConnection
  
  if (connection) {
    connection.addEventListener('change', handleConnectionChange)
  }
  
  // 开始自动检查
  startAutoCheck()
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  
  const connection = (navigator as any).connection || 
                    (navigator as any).mozConnection || 
                    (navigator as any).webkitConnection
  
  if (connection) {
    connection.removeEventListener('change', handleConnectionChange)
  }
  
  // 停止自动检查
  stopAutoCheck()
})

// 暴露方法
defineExpose({
  checkConnection,
  getNetworkInfo: () => networkInfo.value,
  isOnline: () => isOnline.value
})
</script>

<style scoped lang="scss">
.network-status {
  position: relative;
}

.offline-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: #f56c6c;
  color: white;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .offline-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    max-width: 1200px;
    margin: 0 auto;
    
    .offline-icon {
      font-size: 18px;
    }
    
    .offline-text {
      flex: 1;
      text-align: center;
      font-weight: 500;
    }
    
    .el-button {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

.network-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  
  .indicator-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;
  }
  
  &.excellent .indicator-dot {
    background: #67c23a;
    box-shadow: 0 0 8px rgba(103, 194, 58, 0.5);
  }
  
  &.good .indicator-dot {
    background: #e6a23c;
    box-shadow: 0 0 8px rgba(230, 162, 60, 0.5);
  }
  
  &.fair .indicator-dot {
    background: #f56c6c;
    box-shadow: 0 0 8px rgba(245, 108, 108, 0.5);
  }
  
  &.poor .indicator-dot {
    background: #909399;
    box-shadow: 0 0 8px rgba(144, 147, 153, 0.5);
  }
  
  &.offline .indicator-dot {
    background: #f56c6c;
    animation: pulse 2s infinite;
  }
}

// 动画
.slide-down-enter-active,
.slide-down-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .offline-banner {
    .offline-content {
      flex-direction: column;
      gap: 8px;
      text-align: center;
      
      .offline-text {
        font-size: 14px;
      }
    }
  }
  
  .network-indicator {
    bottom: 80px; // 避免与移动端导航栏重叠
    right: 16px;
  }
}
</style>
