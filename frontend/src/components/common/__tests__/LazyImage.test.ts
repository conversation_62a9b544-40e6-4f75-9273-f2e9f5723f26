import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import LazyImage from '../LazyImage.vue'

// Mock IntersectionObserver
const mockIntersectionObserver = vi.fn()
mockIntersectionObserver.mockReturnValue({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
})

global.IntersectionObserver = mockIntersectionObserver

describe('LazyImage', () => {
  let intersectionObserverCallback: (entries: any[]) => void

  beforeEach(() => {
    // 重置mock
    mockIntersectionObserver.mockClear()
    
    // 捕获IntersectionObserver的回调
    mockIntersectionObserver.mockImplementation((callback) => {
      intersectionObserverCallback = callback
      return {
        observe: vi.fn(),
        unobserve: vi.fn(),
        disconnect: vi.fn()
      }
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  const defaultProps = {
    src: 'https://example.com/image.jpg',
    alt: 'Test image'
  }

  it('should render loading state initially', () => {
    const wrapper = mount(LazyImage, {
      props: defaultProps
    })

    expect(wrapper.find('.lazy-image').exists()).toBe(true)
    expect(wrapper.find('.lazy-image-placeholder').exists()).toBe(true)
    expect(wrapper.find('img').exists()).toBe(false)
    expect(wrapper.classes()).toContain('is-loading')
  })

  it('should show loading text when enabled', () => {
    const wrapper = mount(LazyImage, {
      props: {
        ...defaultProps,
        showLoadingText: true,
        loadingText: 'Loading...'
      }
    })

    expect(wrapper.text()).toContain('Loading...')
  })

  it('should hide loading text when disabled', () => {
    const wrapper = mount(LazyImage, {
      props: {
        ...defaultProps,
        showLoadingText: false,
        loadingText: 'Loading...'
      }
    })

    expect(wrapper.text()).not.toContain('Loading...')
  })

  it('should start loading when intersecting', async () => {
    const wrapper = mount(LazyImage, {
      props: defaultProps
    })

    // 模拟元素进入视口
    const mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }

    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    expect(wrapper.find('img').exists()).toBe(true)
  })

  it('should handle successful image load', async () => {
    const wrapper = mount(LazyImage, {
      props: defaultProps
    })

    // 触发intersecting
    const mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    // 模拟图片加载成功
    const img = wrapper.find('img')
    await img.trigger('load')

    expect(wrapper.classes()).not.toContain('is-loading')
    expect(wrapper.classes()).not.toContain('is-error')
    expect(wrapper.find('.lazy-image-placeholder').exists()).toBe(false)
  })

  it('should handle image load error', async () => {
    const wrapper = mount(LazyImage, {
      props: defaultProps
    })

    // 触发intersecting
    const mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    // 模拟图片加载失败
    const img = wrapper.find('img')
    await img.trigger('error')

    expect(wrapper.classes()).toContain('is-error')
    expect(wrapper.find('.lazy-image-error').exists()).toBe(true)
    expect(wrapper.find('img').exists()).toBe(false)
  })

  it('should show error text when enabled', async () => {
    const wrapper = mount(LazyImage, {
      props: {
        ...defaultProps,
        showErrorText: true,
        errorText: 'Failed to load'
      }
    })

    // 触发加载和错误
    const mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    const img = wrapper.find('img')
    await img.trigger('error')

    expect(wrapper.text()).toContain('Failed to load')
  })

  it('should show retry button when enabled', async () => {
    const wrapper = mount(LazyImage, {
      props: {
        ...defaultProps,
        showRetry: true
      }
    })

    // 触发加载和错误
    const mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    const img = wrapper.find('img')
    await img.trigger('error')

    const retryButton = wrapper.find('.error-content .el-button')
    expect(retryButton.exists()).toBe(true)
  })

  it('should retry loading when retry button is clicked', async () => {
    const wrapper = mount(LazyImage, {
      props: {
        ...defaultProps,
        showRetry: true
      }
    })

    // 触发加载和错误
    const mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    const img = wrapper.find('img')
    await img.trigger('error')

    // 点击重试按钮
    const retryButton = wrapper.find('.error-content .el-button')
    await retryButton.trigger('click')

    // 应该重新进入加载状态
    expect(wrapper.classes()).toContain('is-loading')
    expect(wrapper.classes()).not.toContain('is-error')
  })

  it('should apply custom image class', async () => {
    const wrapper = mount(LazyImage, {
      props: {
        ...defaultProps,
        imageClass: 'custom-image-class'
      }
    })

    // 触发intersecting
    const mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    const img = wrapper.find('img')
    expect(img.classes()).toContain('custom-image-class')
  })

  it('should use custom threshold and rootMargin', () => {
    mount(LazyImage, {
      props: {
        ...defaultProps,
        threshold: 0.5,
        rootMargin: '100px'
      }
    })

    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      expect.objectContaining({
        threshold: 0.5,
        rootMargin: '100px'
      })
    )
  })

  it('should disconnect observer on unmount', () => {
    const mockDisconnect = vi.fn()
    mockIntersectionObserver.mockReturnValue({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: mockDisconnect
    })

    const wrapper = mount(LazyImage, {
      props: defaultProps
    })

    wrapper.unmount()

    expect(mockDisconnect).toHaveBeenCalled()
  })

  it('should not load image if not intersecting', async () => {
    const wrapper = mount(LazyImage, {
      props: defaultProps
    })

    // 模拟元素不在视口中
    const mockEntry = {
      isIntersecting: false,
      target: wrapper.element
    }

    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()

    expect(wrapper.find('img').exists()).toBe(false)
    expect(wrapper.find('.lazy-image-placeholder').exists()).toBe(true)
  })

  it('should handle multiple intersection events correctly', async () => {
    const wrapper = mount(LazyImage, {
      props: defaultProps
    })

    // 第一次不相交
    let mockEntry = {
      isIntersecting: false,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()
    expect(wrapper.find('img').exists()).toBe(false)

    // 第二次相交
    mockEntry = {
      isIntersecting: true,
      target: wrapper.element
    }
    intersectionObserverCallback([mockEntry])
    await wrapper.vm.$nextTick()
    expect(wrapper.find('img').exists()).toBe(true)
  })
})
