<template>
  <div class="learning-data-collector">
    <ElCard class="collector-card">
      <template #header>
        <div class="card-header">
          <ElIcon><DataAnalysis /></ElIcon>
          <span>学习数据收集</span>
          <ElTag v-if="accuracyStats" :type="getAccuracyTagType(accuracyStats.overall)" size="small">
            准确度 {{ Math.round(accuracyStats.overall * 100) }}%
          </ElTag>
        </div>
      </template>
      
      <div class="collector-content">
        <!-- 当前会话信息 -->
        <div v-if="currentSession" class="current-session">
          <h4>当前学习会话</h4>
          <div class="session-info">
            <div class="info-item">
              <span class="label">任务：</span>
              <span class="value">{{ currentSession.features.subject }} - {{ currentSession.features.difficulty }}级</span>
            </div>
            <div class="info-item">
              <span class="label">预估时间：</span>
              <span class="value">{{ currentSession.features.estimatedTime }}分钟</span>
            </div>
            <div class="info-item">
              <span class="label">实际用时：</span>
              <span class="value">{{ currentSession.actualTime }}分钟</span>
            </div>
            <div class="info-item">
              <span class="label">完成度：</span>
              <ElProgress
                :percentage="Math.round(currentSession.completionRate * 100)"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="value">{{ Math.round(currentSession.completionRate * 100) }}%</span>
            </div>
          </div>
        </div>
        
        <!-- 用户状态评估 -->
        <div class="user-state-assessment">
          <h4>学习状态评估</h4>
          <div class="state-items">
            <div class="state-item">
              <span class="state-label">精力水平</span>
              <ElRate
                v-model="userState.energy"
                :max="5"
                :colors="stateColors"
                :texts="energyTexts"
                show-text
              />
            </div>
            <div class="state-item">
              <span class="state-label">专注程度</span>
              <ElRate
                v-model="userState.focus"
                :max="5"
                :colors="stateColors"
                :texts="focusTexts"
                show-text
              />
            </div>
            <div class="state-item">
              <span class="state-label">心情状态</span>
              <ElRate
                v-model="userState.mood"
                :max="5"
                :colors="stateColors"
                :texts="moodTexts"
                show-text
              />
            </div>
          </div>
        </div>
        
        <!-- 准确度统计 -->
        <div v-if="accuracyStats" class="accuracy-stats">
          <h4>预估准确度统计</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ Math.round(accuracyStats.overall * 100) }}%</div>
              <div class="stat-label">总体准确度</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ Math.round(accuracyStats.recent * 100) }}%</div>
              <div class="stat-label">近期准确度</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ getTrendIcon(accuracyStats.trend) }}</div>
              <div class="stat-label">准确度趋势</div>
            </div>
          </div>
          
          <!-- 按学科统计 -->
          <div v-if="Object.keys(accuracyStats.bySubject).length > 0" class="subject-stats">
            <h5>各学科准确度</h5>
            <div class="subject-list">
              <div
                v-for="(accuracy, subject) in accuracyStats.bySubject"
                :key="subject"
                class="subject-item"
              >
                <span class="subject-name">{{ subject }}</span>
                <ElProgress
                  :percentage="Math.round(accuracy * 100)"
                  :stroke-width="6"
                  :color="getAccuracyColor(accuracy)"
                />
                <span class="accuracy-value">{{ Math.round(accuracy * 100) }}%</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 学习建议 -->
        <div v-if="suggestions.length > 0" class="learning-suggestions">
          <h4>学习建议</h4>
          <ul class="suggestions-list">
            <li v-for="(suggestion, index) in suggestions" :key="index">
              <ElIcon><InfoFilled /></ElIcon>
              <span>{{ suggestion }}</span>
            </li>
          </ul>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
          <ElButton
            size="small"
            :disabled="!canCollectData"
            @click="collectCurrentData"
          >
            收集当前数据
          </ElButton>
          <ElButton
            size="small"
            type="primary"
            @click="exportData"
          >
            导出学习数据
          </ElButton>
          <ElButton
            size="small"
            type="danger"
            plain
            @click="resetData"
          >
            重置数据
          </ElButton>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ElButton, ElCard, ElIcon, ElMessage, ElMessageBox, ElProgress, ElRate, ElTag } from 'element-plus'
import { DataAnalysis, InfoFilled, TrendCharts } from '@element-plus/icons-vue'
import TimeEstimationService, { type LearningSession } from '@/services/TimeEstimationService'

interface Props {
  currentSession?: LearningSession | null
}

const props = defineProps<Props>()

// 响应式数据
const userState = ref({
  energy: 3,
  focus: 3,
  mood: 3
})

// 服务实例
const estimationService = TimeEstimationService.getInstance()

// 状态评分颜色和文本
const stateColors = ['#f56c6c', '#e6a23c', '#6b7280', '#409eff', '#67c23a']
const energyTexts = ['疲惫', '较累', '一般', '良好', '充沛']
const focusTexts = ['分散', '较差', '一般', '集中', '专注']
const moodTexts = ['糟糕', '较差', '一般', '良好', '愉快']

// 计算属性
const accuracyStats = computed(() => estimationService.getAccuracyStats())

const canCollectData = computed(() => {
  return props.currentSession && 
         userState.value.energy > 0 && 
         userState.value.focus > 0 && 
         userState.value.mood > 0
})

const suggestions = computed(() => {
  const suggestions: string[] = []
  
  if (accuracyStats.value.overall < 0.6) {
    suggestions.push('预估准确度较低，建议更仔细地评估任务复杂度')
  }
  
  if (accuracyStats.value.trend === 'declining') {
    suggestions.push('预估准确度呈下降趋势，建议调整学习策略')
  }
  
  if (userState.value.energy < 3) {
    suggestions.push('当前精力水平较低，建议适当休息后再学习')
  }
  
  if (userState.value.focus < 3) {
    suggestions.push('专注度不足，建议选择更安静的学习环境')
  }
  
  return suggestions
})

// 方法
const getAccuracyTagType = (accuracy: number) => {
  if (accuracy >= 0.8) {return 'success'}
  if (accuracy >= 0.6) {return 'warning'}
  return 'danger'
}

const getAccuracyColor = (accuracy: number) => {
  if (accuracy >= 0.8) {return '#67c23a'}
  if (accuracy >= 0.6) {return '#e6a23c'}
  return '#f56c6c'
}

const getTrendIcon = (trend: string) => {
  switch (trend) {
    case 'improving': return '📈'
    case 'declining': return '📉'
    default: return '➡️'
  }
}

const collectCurrentData = () => {
  if (!props.currentSession) {
    ElMessage.warning('没有当前学习会话数据')
    return
  }
  
  const sessionWithState: LearningSession = {
    ...props.currentSession,
    userState: { ...userState.value },
    timestamp: new Date().toISOString(),
    timeOfDay: new Date().getHours(),
    dayOfWeek: new Date().getDay()
  }
  
  estimationService.updateLearningHistory(sessionWithState)
  ElMessage.success('学习数据已收集')
}

const exportData = () => {
  try {
    const data = {
      userProfile: estimationService.getUserProfile?.() || {},
      accuracyStats: accuracyStats.value,
      timestamp: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `learning-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('学习数据已导出')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const resetData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有学习数据吗？此操作不可恢复。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    localStorage.removeItem('learning-history')
    localStorage.removeItem('user-profile')
    
    ElMessage.success('学习数据已重置')
    
    // 刷新页面以重新初始化
    window.location.reload()
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped lang="scss">
.learning-data-collector {
  .collector-card {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.collector-content {
  .current-session {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .session-info {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .label {
          min-width: 80px;
          color: #606266;
          font-size: 14px;
        }
        
        .value {
          color: #303133;
          font-weight: 500;
          margin-left: 8px;
        }
        
        .el-progress {
          flex: 1;
          margin: 0 8px;
        }
      }
    }
  }
  
  .user-state-assessment {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .state-items {
      .state-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .state-label {
          min-width: 80px;
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
  
  .accuracy-stats {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .stat-item {
      text-align: center;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 8px;
      
      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
    
    .subject-stats {
      h5 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
      }
      
      .subject-list {
        .subject-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .subject-name {
            min-width: 60px;
            font-size: 12px;
            color: #606266;
          }
          
          .el-progress {
            flex: 1;
            margin: 0 8px;
          }
          
          .accuracy-value {
            min-width: 40px;
            font-size: 12px;
            color: #303133;
            text-align: right;
          }
        }
      }
    }
  }
  
  .learning-suggestions {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .suggestions-list {
      margin: 0;
      padding: 0;
      list-style: none;
      
      li {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 8px;
        padding: 8px;
        background: #fff7e6;
        border-radius: 6px;
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
        
        .el-icon {
          color: #e6a23c;
          margin-top: 2px;
          flex-shrink: 0;
        }
      }
    }
  }
  
  .actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .collector-content {
    .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
