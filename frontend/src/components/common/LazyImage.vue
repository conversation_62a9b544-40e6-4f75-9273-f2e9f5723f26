<template>
  <div class="lazy-image" :class="{ 'is-loading': isLoading, 'is-error': isError }">
    <img
      v-if="shouldLoad && !isError"
      :src="src"
      :alt="alt"
      :class="imageClass"
      @load="handleLoad"
      @error="handleError"
    />
    
    <!-- 占位符 -->
    <div v-if="isLoading" class="lazy-image-placeholder">
      <div class="placeholder-content">
        <ElIcon class="loading-icon">
          <Loading />
        </ElIcon>
        <span v-if="showLoadingText">{{ loadingText }}</span>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="isError" class="lazy-image-error">
      <div class="error-content">
        <ElIcon class="error-icon">
          <Picture />
        </ElIcon>
        <span v-if="showErrorText">{{ errorText }}</span>
        <ElButton v-if="showRetry" size="small" @click="retry">
          重试
        </ElButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { Loading, Picture } from '@element-plus/icons-vue'

interface Props {
  src: string
  alt?: string
  placeholder?: string
  errorImage?: string
  loadingText?: string
  errorText?: string
  showLoadingText?: boolean
  showErrorText?: boolean
  showRetry?: boolean
  imageClass?: string
  threshold?: number
  rootMargin?: string
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  loadingText: '加载中...',
  errorText: '加载失败',
  showLoadingText: true,
  showErrorText: true,
  showRetry: true,
  threshold: 0.1,
  rootMargin: '50px'
})

// 响应式数据
const imageRef = ref<HTMLElement>()
const isLoading = ref(true)
const isError = ref(false)
const shouldLoad = ref(false)
const observer = ref<IntersectionObserver>()

// 方法
const handleLoad = () => {
  isLoading.value = false
  isError.value = false
}

const handleError = () => {
  isLoading.value = false
  isError.value = true
}

const retry = () => {
  isLoading.value = true
  isError.value = false
  shouldLoad.value = false
  
  // 重新触发加载
  setTimeout(() => {
    shouldLoad.value = true
  }, 100)
}

const startObserving = () => {
  if (!imageRef.value) {return}

  observer.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !shouldLoad.value) {
          shouldLoad.value = true
          observer.value?.unobserve(entry.target)
        }
      })
    },
    {
      threshold: props.threshold,
      rootMargin: props.rootMargin
    }
  )

  observer.value.observe(imageRef.value)
}

const stopObserving = () => {
  if (observer.value) {
    observer.value.disconnect()
    observer.value = undefined
  }
}

// 生命周期
onMounted(() => {
  startObserving()
})

onUnmounted(() => {
  stopObserving()
})
</script>

<style scoped lang="scss">
.lazy-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background: #f5f7fa;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
  }
  
  .lazy-image-placeholder,
  .lazy-image-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
  }
  
  .placeholder-content,
  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #909399;
    font-size: 12px;
  }
  
  .loading-icon {
    font-size: 24px;
    animation: rotate 1s linear infinite;
  }
  
  .error-icon {
    font-size: 24px;
    color: #f56c6c;
  }
  
  &.is-loading {
    .placeholder-content {
      opacity: 1;
    }
  }
  
  &.is-error {
    .error-content {
      opacity: 1;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
.lazy-image {
  min-height: 100px;
  
  @media (max-width: 768px) {
    min-height: 80px;
    
    .placeholder-content,
    .error-content {
      font-size: 10px;
      gap: 4px;
    }
    
    .loading-icon,
    .error-icon {
      font-size: 20px;
    }
  }
}
</style>
