<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <AppHeader />

    <!-- 主体内容区 -->
    <div class="app-main">
      <!-- 侧边栏 -->
      <AppSidebar
        :collapsed="appStore.sidebarCollapsed"
        :mobile-open="appStore.mobileMenuOpen"
        @toggle="appStore.toggleSidebar"
        @mobile-toggle="appStore.toggleMobileMenu"
      />

      <!-- 内容区域 -->
      <div class="app-content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useAppStore } from '@/stores/app'
  import AppHeader from './AppHeader.vue'
  import AppSidebar from './AppSidebar.vue'

  const appStore = useAppStore()
</script>

<style scoped>
  .app-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .app-main {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .app-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: var(--el-bg-color-page);
  }

  @media (max-width: 768px) {
    .app-content {
      padding: 12px;
    }
  }
</style>
