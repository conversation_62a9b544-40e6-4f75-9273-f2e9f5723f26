<template>
  <div class="calendar-view">
    <div class="calendar-header">
      <div class="calendar-controls">
        <el-button-group>
          <el-button :icon="ArrowLeft" @click="previousMonth" />
          <el-button @click="goToToday">今天</el-button>
          <el-button :icon="ArrowRight" @click="nextMonth" />
        </el-button-group>
        
        <h3 class="calendar-title">{{ currentMonthTitle }}</h3>
        
        <div class="view-options">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button value="month">月视图</el-radio-button>
            <el-radio-button value="week">周视图</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      
      <div class="calendar-legend">
        <div class="legend-item">
          <div class="legend-dot light"></div>
          <span>轻度负载</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot medium"></div>
          <span>中度负载</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot heavy"></div>
          <span>重度负载</span>
        </div>
      </div>
    </div>

    <div class="calendar-container">
      <!-- 星期标题 -->
      <div class="calendar-weekdays">
        <div 
          v-for="day in weekdays" 
          :key="day"
          class="weekday-header"
        >
          {{ day }}
        </div>
      </div>

      <!-- 日历网格 -->
      <div class="calendar-grid">
        <div
          v-for="date in calendarDates"
          :key="date.dateStr"
          class="calendar-cell"
          :class="getCellClass(date)"
          @click="handleDateClick(date)"
        >
          <div class="date-number">{{ date.day }}</div>
          
          <div v-if="date.tasks.length > 0" class="date-tasks">
            <div 
              v-for="task in date.tasks.slice(0, 3)"
              :key="task.id"
              class="task-item"
              :class="getTaskClass(task)"
              @click.stop="handleTaskClick(task)"
            >
              <div class="task-dot"></div>
              <span class="task-title">{{ task.title }}</span>
            </div>
            
            <div 
              v-if="date.tasks.length > 3"
              class="more-tasks"
              @click.stop="showMoreTasks(date)"
            >
              +{{ date.tasks.length - 3 }}个任务
            </div>
          </div>

          <div v-if="date.loadLevel" class="load-indicator" :class="date.loadLevel">
            <div class="load-bar"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog
      v-model="showTaskDialog"
      title="任务详情"
      width="500px"
    >
      <div v-if="selectedTask" class="task-detail">
        <h4>{{ selectedTask.title }}</h4>
        <p>{{ selectedTask.content }}</p>
        <div class="task-meta">
          <el-tag>{{ getSubjectName(selectedTask.subject) }}</el-tag>
          <span>预计时间：{{ selectedTask.estimatedTime }}分钟</span>
          <span>难度：{{ selectedTask.difficulty }}/5</span>
        </div>
      </div>
      <template #footer>
        <el-button @click="showTaskDialog = false">关闭</el-button>
        <el-button type="primary" @click="startTask">开始任务</el-button>
      </template>
    </el-dialog>

    <!-- 更多任务弹窗 -->
    <el-dialog
      v-model="showMoreDialog"
      :title="`${selectedDate?.dateStr} 的任务`"
      width="600px"
    >
      <div v-if="selectedDate" class="more-tasks-list">
        <div
          v-for="task in selectedDate.tasks"
          :key="task.id"
          class="task-list-item"
          @click="handleTaskClick(task)"
        >
          <div class="task-info">
            <h5>{{ task.title }}</h5>
            <p>{{ task.content }}</p>
            <div class="task-tags">
              <el-tag size="small">{{ getSubjectName(task.subject) }}</el-tag>
              <el-tag size="small" type="info">{{ task.estimatedTime }}分钟</el-tag>
            </div>
          </div>
          <el-button size="small" type="primary">开始</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue'
  import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
  import type { Task } from '@/types'
  import dayjs from 'dayjs'

  interface CalendarDate {
    date: Date
    dateStr: string
    day: number
    isCurrentMonth: boolean
    isToday: boolean
    tasks: Task[]
    loadLevel?: 'light' | 'medium' | 'heavy'
  }

  interface Props {
    tasks: Task[]
  }

  interface Emits {
    (e: 'task-click', task: Task): void
    (e: 'date-click', date: CalendarDate): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const currentDate = ref(new Date())
  const viewMode = ref<'month' | 'week'>('month')
  const showTaskDialog = ref(false)
  const showMoreDialog = ref(false)
  const selectedTask = ref<Task | null>(null)
  const selectedDate = ref<CalendarDate | null>(null)

  const weekdays = ['日', '一', '二', '三', '四', '五', '六']

  // 计算属性
  const currentMonthTitle = computed(() => {
    return dayjs(currentDate.value).format('YYYY年MM月')
  })

  const calendarDates = computed((): CalendarDate[] => {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth()
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    
    // 获取日历开始日期（包含上月末尾几天）
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())
    
    // 获取日历结束日期（包含下月开始几天）
    const endDate = new Date(lastDay)
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()))
    
    const dates: CalendarDate[] = []
    const current = new Date(startDate)
    const today = new Date()
    
    while (current <= endDate) {
      const dateStr = dayjs(current).format('YYYY-MM-DD')
      const dateTasks = getTasksForDate(current)
      
      dates.push({
        date: new Date(current),
        dateStr,
        day: current.getDate(),
        isCurrentMonth: current.getMonth() === month,
        isToday: dayjs(current).isSame(today, 'day'),
        tasks: dateTasks,
        loadLevel: calculateLoadLevel(dateTasks)
      })
      
      current.setDate(current.getDate() + 1)
    }
    
    return dates
  })

  // 方法
  const getTasksForDate = (date: Date): Task[] => {
    const dateStr = dayjs(date).format('YYYY-MM-DD')
    return props.tasks.filter(task => {
      const taskDate = dayjs(task.nextReviewTime).format('YYYY-MM-DD')
      return taskDate === dateStr
    })
  }

  const calculateLoadLevel = (tasks: Task[]): 'light' | 'medium' | 'heavy' | undefined => {
    const totalTime = tasks.reduce((sum, task) => sum + task.estimatedTime, 0)
    
    if (totalTime === 0) {return undefined}
    if (totalTime <= 60) {return 'light'}
    if (totalTime <= 120) {return 'medium'}
    return 'heavy'
  }

  const getCellClass = (date: CalendarDate): string => {
    const classes = ['calendar-cell']
    
    if (!date.isCurrentMonth) {classes.push('other-month')}
    if (date.isToday) {classes.push('today')}
    if (date.tasks.length > 0) {classes.push('has-tasks')}
    if (date.loadLevel) {classes.push(`load-${date.loadLevel}`)}
    
    return classes.join(' ')
  }

  const getTaskClass = (task: Task): string => {
    const priority = task.priority
    if (priority >= 4) {return 'high-priority'}
    if (priority >= 3) {return 'medium-priority'}
    return 'low-priority'
  }

  const getSubjectName = (subject: string): string => {
    const nameMap: Record<string, string> = {
      chinese: '语文',
      math: '数学',
      english: '英语',
      physics: '物理',
      chemistry: '化学',
      biology: '生物',
      history: '历史',
      geography: '地理'
    }
    return nameMap[subject] || subject
  }

  const previousMonth = () => {
    const newDate = new Date(currentDate.value)
    newDate.setMonth(newDate.getMonth() - 1)
    currentDate.value = newDate
  }

  const nextMonth = () => {
    const newDate = new Date(currentDate.value)
    newDate.setMonth(newDate.getMonth() + 1)
    currentDate.value = newDate
  }

  const goToToday = () => {
    currentDate.value = new Date()
  }

  const handleDateClick = (date: CalendarDate) => {
    emit('date-click', date)
  }

  const handleTaskClick = (task: Task) => {
    selectedTask.value = task
    showTaskDialog.value = true
    emit('task-click', task)
  }

  const showMoreTasks = (date: CalendarDate) => {
    selectedDate.value = date
    showMoreDialog.value = true
  }

  const startTask = () => {
    if (selectedTask.value) {
      // TODO: 实现开始任务逻辑
      showTaskDialog.value = false
    }
  }

  onMounted(() => {
    // 组件挂载后的初始化逻辑
  })
</script>

<style scoped>
  .calendar-view {
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 20px;
  }

  .calendar-header {
    margin-bottom: 20px;
  }

  .calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .calendar-title {
    margin: 0;
    color: var(--el-text-color-primary);
    font-size: 20px;
    font-weight: 600;
  }

  .calendar-legend {
    display: flex;
    gap: 20px;
    justify-content: center;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .legend-dot.light {
    background: #67C23A;
  }

  .legend-dot.medium {
    background: #E6A23C;
  }

  .legend-dot.heavy {
    background: #F56C6C;
  }

  .calendar-container {
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    overflow: hidden;
  }

  .calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: var(--el-fill-color-light);
  }

  .weekday-header {
    padding: 12px;
    text-align: center;
    font-weight: 600;
    color: var(--el-text-color-primary);
    border-right: 1px solid var(--el-border-color-lighter);
  }

  .weekday-header:last-child {
    border-right: none;
  }

  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
  }

  .calendar-cell {
    min-height: 120px;
    padding: 8px;
    border-right: 1px solid var(--el-border-color-lighter);
    border-bottom: 1px solid var(--el-border-color-lighter);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
  }

  .calendar-cell:hover {
    background: var(--el-fill-color-light);
  }

  .calendar-cell:nth-child(7n) {
    border-right: none;
  }

  .calendar-cell.other-month {
    background: var(--el-fill-color-lighter);
    color: var(--el-text-color-disabled);
  }

  .calendar-cell.today {
    background: var(--el-color-primary-light-9);
  }

  .calendar-cell.today .date-number {
    background: var(--el-color-primary);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .date-number {
    font-weight: 600;
    margin-bottom: 4px;
  }

  .date-tasks {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .task-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .task-item:hover {
    background: var(--el-fill-color);
  }

  .task-item.high-priority {
    background: var(--el-color-danger-light-9);
    border-left: 3px solid var(--el-color-danger);
  }

  .task-item.medium-priority {
    background: var(--el-color-warning-light-9);
    border-left: 3px solid var(--el-color-warning);
  }

  .task-item.low-priority {
    background: var(--el-color-success-light-9);
    border-left: 3px solid var(--el-color-success);
  }

  .task-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    flex-shrink: 0;
  }

  .task-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .more-tasks {
    font-size: 11px;
    color: var(--el-color-primary);
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 4px;
  }

  .more-tasks:hover {
    background: var(--el-color-primary-light-9);
  }

  .load-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
  }

  .load-bar {
    height: 100%;
    border-radius: 2px 2px 0 0;
  }

  .load-light .load-bar {
    background: #67C23A;
  }

  .load-medium .load-bar {
    background: #E6A23C;
  }

  .load-heavy .load-bar {
    background: #F56C6C;
  }

  .task-detail h4 {
    margin: 0 0 12px 0;
    color: var(--el-text-color-primary);
  }

  .task-detail p {
    color: var(--el-text-color-regular);
    line-height: 1.6;
    margin-bottom: 16px;
  }

  .task-meta {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }

  .more-tasks-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .task-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .task-list-item:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .task-info h5 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-primary);
  }

  .task-info p {
    margin: 0 0 8px 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .task-tags {
    display: flex;
    gap: 6px;
  }

  @media (max-width: 768px) {
    .calendar-view {
      padding: 12px;
    }

    .calendar-controls {
      flex-direction: column;
      gap: 12px;
    }

    .calendar-legend {
      flex-wrap: wrap;
      gap: 12px;
    }

    .calendar-cell {
      min-height: 80px;
      padding: 4px;
    }

    .task-item {
      font-size: 10px;
    }

    .date-number {
      font-size: 14px;
    }
  }
</style>
