<template>
  <div class="load-indicator">
    <div class="indicator-header">
      <h4>学习负载指示器</h4>
      <el-tooltip content="基于任务数量和预估时间计算的学习负载等级">
        <el-icon class="help-icon"><QuestionFilled /></el-icon>
      </el-tooltip>
    </div>

    <div class="load-levels">
      <div 
        v-for="level in loadLevels"
        :key="level.key"
        class="load-level"
        :class="{ active: currentLevel === level.key }"
      >
        <div class="level-indicator" :style="{ backgroundColor: level.color }">
          <div class="level-dot"></div>
        </div>
        <div class="level-info">
          <span class="level-name">{{ level.name }}</span>
          <span class="level-range">{{ level.range }}</span>
        </div>
      </div>
    </div>

    <div class="current-load">
      <div class="load-summary">
        <div class="summary-item">
          <span class="label">当前负载：</span>
          <span class="value" :style="{ color: currentLevelColor }">
            {{ currentLevelName }}
          </span>
        </div>
        <div class="summary-item">
          <span class="label">总时长：</span>
          <span class="value">{{ totalTime }}分钟</span>
        </div>
        <div class="summary-item">
          <span class="label">任务数：</span>
          <span class="value">{{ taskCount }}个</span>
        </div>
      </div>

      <div class="load-progress">
        <el-progress
          :percentage="loadPercentage"
          :color="progressColors"
          :stroke-width="8"
          :show-text="false"
        />
        <div class="progress-labels">
          <span>轻度</span>
          <span>中度</span>
          <span>重度</span>
        </div>
      </div>
    </div>

    <div v-if="suggestions.length > 0" class="load-suggestions">
      <h5>负载建议</h5>
      <ul class="suggestion-list">
        <li 
          v-for="(suggestion, index) in suggestions"
          :key="index"
          class="suggestion-item"
        >
          <el-icon class="suggestion-icon"><InfoFilled /></el-icon>
          <span>{{ suggestion }}</span>
        </li>
      </ul>
    </div>

    <div class="load-chart">
      <h5>负载分布图</h5>
      <div class="chart-container">
        <div 
          v-for="(bar, index) in chartData"
          :key="index"
          class="chart-bar"
          :style="{ 
            height: `${bar.height}%`,
            backgroundColor: bar.color 
          }"
        >
          <div class="bar-label">{{ bar.label }}</div>
          <div class="bar-value">{{ bar.value }}min</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { InfoFilled, QuestionFilled } from '@element-plus/icons-vue'
  import type { Task } from '@/types'

  interface Props {
    tasks: Task[]
    selectedDate?: string
  }

  const props = defineProps<Props>()

  const loadLevels = [
    {
      key: 'light',
      name: '轻度负载',
      range: '0-60分钟',
      color: '#67C23A',
      min: 0,
      max: 60
    },
    {
      key: 'medium',
      name: '中度负载',
      range: '60-120分钟',
      color: '#E6A23C',
      min: 60,
      max: 120
    },
    {
      key: 'heavy',
      name: '重度负载',
      range: '120分钟以上',
      color: '#F56C6C',
      min: 120,
      max: Infinity
    }
  ]

  const progressColors = [
    { color: '#67C23A', percentage: 33 },
    { color: '#E6A23C', percentage: 66 },
    { color: '#F56C6C', percentage: 100 }
  ]

  // 计算属性
  const totalTime = computed(() => {
    return props.tasks.reduce((sum, task) => sum + task.estimatedTime, 0)
  })

  const taskCount = computed(() => {
    return props.tasks.length
  })

  const currentLevel = computed(() => {
    const time = totalTime.value
    if (time <= 60) {return 'light'}
    if (time <= 120) {return 'medium'}
    return 'heavy'
  })

  const currentLevelName = computed(() => {
    const level = loadLevels.find(l => l.key === currentLevel.value)
    return level ? level.name : '未知'
  })

  const currentLevelColor = computed(() => {
    const level = loadLevels.find(l => l.key === currentLevel.value)
    return level ? level.color : '#909399'
  })

  const loadPercentage = computed(() => {
    const time = totalTime.value
    if (time <= 60) {return (time / 60) * 33}
    if (time <= 120) {return 33 + ((time - 60) / 60) * 33}
    return Math.min(66 + ((time - 120) / 120) * 34, 100)
  })

  const suggestions = computed(() => {
    const time = totalTime.value
    const count = taskCount.value
    const suggestions: string[] = []

    if (time === 0) {
      suggestions.push('今天没有安排任务，可以考虑复习之前的内容')
    } else if (time <= 30) {
      suggestions.push('负载较轻，可以考虑增加一些复习任务')
    } else if (time <= 60) {
      suggestions.push('负载适中，保持良好的学习节奏')
    } else if (time <= 120) {
      suggestions.push('负载较重，建议合理安排休息时间')
      suggestions.push('可以将部分任务分散到其他时间')
    } else {
      suggestions.push('负载过重，强烈建议重新安排任务')
      suggestions.push('避免长时间连续学习，注意劳逸结合')
      suggestions.push('考虑将部分任务延后或分解')
    }

    if (count > 5) {
      suggestions.push('任务数量较多，建议按优先级排序')
    }

    return suggestions
  })

  const chartData = computed(() => {
    const subjects: Record<string, { time: number; count: number }> = {}
    
    props.tasks.forEach(task => {
      if (!subjects[task.subject]) {
        subjects[task.subject] = { time: 0, count: 0 }
      }
      subjects[task.subject].time += task.estimatedTime
      subjects[task.subject].count += 1
    })

    const maxTime = Math.max(...Object.values(subjects).map(s => s.time), 1)
    
    return Object.entries(subjects).map(([subject, data]) => ({
      label: getSubjectName(subject),
      value: data.time,
      height: (data.time / maxTime) * 100,
      color: getSubjectColor(subject)
    }))
  })

  const getSubjectName = (subject: string): string => {
    const nameMap: Record<string, string> = {
      chinese: '语文',
      math: '数学',
      english: '英语',
      physics: '物理',
      chemistry: '化学',
      biology: '生物',
      history: '历史',
      geography: '地理'
    }
    return nameMap[subject] || subject
  }

  const getSubjectColor = (subject: string): string => {
    const colorMap: Record<string, string> = {
      chinese: '#E74C3C',
      math: '#3498DB',
      english: '#9B59B6',
      physics: '#E67E22',
      chemistry: '#1ABC9C',
      biology: '#27AE60',
      history: '#F39C12',
      geography: '#34495E'
    }
    return colorMap[subject] || '#95A5A6'
  }
</script>

<style scoped>
  .load-indicator {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    padding: 20px;
  }

  .indicator-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
  }

  .indicator-header h4 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .help-icon {
    color: var(--el-text-color-secondary);
    cursor: help;
  }

  .load-levels {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
  }

  .load-level {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .load-level:hover {
    background: var(--el-fill-color-light);
  }

  .load-level.active {
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-7);
  }

  .level-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .level-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
  }

  .level-info {
    display: flex;
    flex-direction: column;
  }

  .level-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    font-size: 14px;
  }

  .level-range {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .current-load {
    margin-bottom: 24px;
  }

  .load-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 12px;
    background: var(--el-fill-color-light);
    border-radius: 8px;
  }

  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .summary-item .label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .summary-item .value {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .load-progress {
    position: relative;
  }

  .progress-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .load-suggestions {
    margin-bottom: 24px;
  }

  .load-suggestions h5 {
    margin: 0 0 12px 0;
    color: var(--el-text-color-primary);
  }

  .suggestion-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .suggestion-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
    line-height: 1.5;
  }

  .suggestion-icon {
    color: var(--el-color-warning);
    margin-top: 2px;
    flex-shrink: 0;
  }

  .load-chart h5 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
  }

  .chart-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    height: 120px;
    padding: 0 8px;
  }

  .chart-bar {
    flex: 1;
    min-height: 20px;
    border-radius: 4px 4px 0 0;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    transition: all 0.3s ease;
  }

  .chart-bar:hover {
    opacity: 0.8;
    transform: translateY(-2px);
  }

  .bar-label {
    position: absolute;
    bottom: -20px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    white-space: nowrap;
  }

  .bar-value {
    color: white;
    font-size: 10px;
    font-weight: 600;
    margin-bottom: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  @media (max-width: 768px) {
    .load-indicator {
      padding: 16px;
    }

    .load-levels {
      flex-direction: column;
      gap: 8px;
    }

    .load-summary {
      flex-direction: column;
      gap: 12px;
    }

    .summary-item {
      flex-direction: row;
      justify-content: space-between;
    }

    .chart-container {
      height: 100px;
      gap: 8px;
    }

    .bar-value {
      font-size: 9px;
    }
  }
</style>
