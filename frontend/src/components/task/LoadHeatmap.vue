<template>
  <div class="load-heatmap">
    <div class="heatmap-header">
      <h4>{{ monthTitle }}</h4>
      <div class="heatmap-controls">
        <el-button-group size="small">
          <el-button :icon="ArrowLeft" @click="previousMonth" />
          <el-button @click="currentMonth">本月</el-button>
          <el-button :icon="ArrowRight" @click="nextMonth" />
        </el-button-group>
      </div>
    </div>

    <div class="heatmap-content">
      <!-- 星期标题 -->
      <div class="weekdays">
        <div
          v-for="day in weekdays"
          :key="day"
          class="weekday"
        >
          {{ day }}
        </div>
      </div>

      <!-- 热力图网格 -->
      <div class="heatmap-grid">
        <div
          v-for="(week, weekIndex) in calendarWeeks"
          :key="weekIndex"
          class="heatmap-week"
        >
          <div
            v-for="(day, dayIndex) in week"
            :key="dayIndex"
            class="heatmap-day"
            :class="getDayClass(day)"
            :style="getDayStyle(day)"
            @mouseenter="showTooltip($event, day)"
            @mouseleave="hideTooltip"
            @click="handleDayClick(day)"
          >
            <span class="day-number">{{ day.day }}</span>
            <div v-if="day.value > 0" class="day-indicator">
              <div class="indicator-dot" :class="`level-${day.level}`"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图例 -->
      <div class="heatmap-legend">
        <span class="legend-label">负载强度:</span>
        <div class="legend-scale">
          <div class="scale-item">
            <div class="scale-color level-0"></div>
            <span>无</span>
          </div>
          <div
            v-for="level in 5"
            :key="level"
            class="scale-item"
          >
            <div class="scale-color" :class="`level-${level}`"></div>
            <span v-if="level === 1">轻</span>
            <span v-else-if="level === 5">重</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="heatmap-stats">
      <div class="stat-item">
        <span class="stat-label">总学习时长:</span>
        <span class="stat-value">{{ totalTime }}分钟</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">平均每日:</span>
        <span class="stat-value">{{ averageDailyTime }}分钟</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">最高负载:</span>
        <span class="stat-value">{{ maxDailyTime }}分钟</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">学习天数:</span>
        <span class="stat-value">{{ studyDays }}天</span>
      </div>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="tooltip.visible"
      class="heatmap-tooltip"
      :style="{ 
        left: `${tooltip.x}px`, 
        top: `${tooltip.y}px` 
      }"
    >
      <div class="tooltip-content">
        <div class="tooltip-date">{{ tooltip.data?.date }}</div>
        <div class="tooltip-value">
          学习时长: {{ tooltip.data?.value || 0 }}分钟
        </div>
        <div class="tooltip-level">
          负载等级: {{ getLevelText(tooltip.data?.level || 0) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive, ref } from 'vue'
  import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
  import dayjs from 'dayjs'

  interface HeatmapData {
    date: string
    value: number
    level: number
  }

  interface CalendarDay {
    date: string
    day: number
    value: number
    level: number
    isCurrentMonth: boolean
    isToday: boolean
  }

  interface Props {
    data: HeatmapData[]
    month: string
  }

  interface Emits {
    (e: 'month-change', month: string): void
    (e: 'day-click', day: CalendarDay): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const currentMonthRef = ref(props.month)
  const tooltip = reactive({
    visible: false,
    x: 0,
    y: 0,
    data: null as CalendarDay | null
  })

  const weekdays = ['日', '一', '二', '三', '四', '五', '六']

  // 计算属性
  const monthTitle = computed(() => {
    return dayjs(currentMonthRef.value).format('YYYY年MM月')
  })

  const calendarWeeks = computed((): CalendarDay[][] => {
    const month = dayjs(currentMonthRef.value)
    const firstDay = month.startOf('month')
    const lastDay = month.endOf('month')
    const startDate = firstDay.startOf('week')
    const endDate = lastDay.endOf('week')

    const weeks: CalendarDay[][] = []
    let currentWeek: CalendarDay[] = []
    let current = startDate

    while (current.isBefore(endDate) || current.isSame(endDate, 'day')) {
      const dateStr = current.format('YYYY-MM-DD')
      const dayData = props.data.find(item => item.date === dateStr)
      
      const day: CalendarDay = {
        date: dateStr,
        day: current.date(),
        value: dayData?.value || 0,
        level: dayData?.level || 0,
        isCurrentMonth: current.isSame(month, 'month'),
        isToday: current.isSame(dayjs(), 'day')
      }

      currentWeek.push(day)

      if (currentWeek.length === 7) {
        weeks.push(currentWeek)
        currentWeek = []
      }

      current = current.add(1, 'day')
    }

    if (currentWeek.length > 0) {
      weeks.push(currentWeek)
    }

    return weeks
  })

  const totalTime = computed(() => {
    return props.data.reduce((sum, item) => sum + item.value, 0)
  })

  const averageDailyTime = computed(() => {
    const daysWithData = props.data.filter(item => item.value > 0).length
    if (daysWithData === 0) {return 0}
    return Math.round(totalTime.value / daysWithData)
  })

  const maxDailyTime = computed(() => {
    return Math.max(...props.data.map(item => item.value), 0)
  })

  const studyDays = computed(() => {
    return props.data.filter(item => item.value > 0).length
  })

  // 方法
  const getDayClass = (day: CalendarDay): string => {
    const classes = ['heatmap-day']
    
    if (!day.isCurrentMonth) {classes.push('other-month')}
    if (day.isToday) {classes.push('today')}
    if (day.value > 0) {classes.push('has-data')}
    
    return classes.join(' ')
  }

  const getDayStyle = (day: CalendarDay) => {
    if (day.level === 0) {return {}}
    
    const opacity = Math.min(day.level / 5, 1)
    const colors = {
      1: '#c6e48b',
      2: '#7bc96f',
      3: '#239a3b',
      4: '#196127',
      5: '#0d2818'
    }
    
    return {
      backgroundColor: colors[day.level as keyof typeof colors] || colors[1],
      opacity: 0.3 + (opacity * 0.7)
    }
  }

  const getLevelText = (level: number): string => {
    const texts = {
      0: '无负载',
      1: '轻度负载',
      2: '轻中度负载',
      3: '中度负载',
      4: '中重度负载',
      5: '重度负载'
    }
    return texts[level as keyof typeof texts] || '未知'
  }

  const previousMonth = () => {
    const newMonth = dayjs(currentMonthRef.value).subtract(1, 'month').format('YYYY-MM')
    currentMonthRef.value = newMonth
    emit('month-change', newMonth)
  }

  const nextMonth = () => {
    const newMonth = dayjs(currentMonthRef.value).add(1, 'month').format('YYYY-MM')
    currentMonthRef.value = newMonth
    emit('month-change', newMonth)
  }

  const currentMonth = () => {
    const newMonth = dayjs().format('YYYY-MM')
    currentMonthRef.value = newMonth
    emit('month-change', newMonth)
  }

  const showTooltip = (event: MouseEvent, day: CalendarDay) => {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    tooltip.visible = true
    tooltip.x = event.clientX - rect.left + 10
    tooltip.y = event.clientY - rect.top - 10
    tooltip.data = day
  }

  const hideTooltip = () => {
    tooltip.visible = false
    tooltip.data = null
  }

  const handleDayClick = (day: CalendarDay) => {
    emit('day-click', day)
  }
</script>

<style scoped>
  .load-heatmap {
    padding: 16px;
  }

  .heatmap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .heatmap-header h4 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .heatmap-content {
    margin-bottom: 16px;
  }

  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 8px;
  }

  .weekday {
    text-align: center;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    padding: 4px;
  }

  .heatmap-grid {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .heatmap-week {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
  }

  .heatmap-day {
    aspect-ratio: 1;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    background: var(--el-fill-color-lighter);
  }

  .heatmap-day:hover {
    border-color: var(--el-color-primary);
    transform: scale(1.1);
    z-index: 10;
  }

  .heatmap-day.other-month {
    opacity: 0.3;
  }

  .heatmap-day.today {
    border-color: var(--el-color-primary);
    border-width: 2px;
  }

  .heatmap-day.has-data {
    color: white;
    font-weight: 600;
  }

  .day-number {
    font-size: 10px;
    line-height: 1;
  }

  .day-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
  }

  .indicator-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
  }

  .indicator-dot.level-1 {
    background: #c6e48b;
  }

  .indicator-dot.level-2 {
    background: #7bc96f;
  }

  .indicator-dot.level-3 {
    background: #239a3b;
  }

  .indicator-dot.level-4 {
    background: #196127;
  }

  .indicator-dot.level-5 {
    background: #0d2818;
  }

  .heatmap-legend {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .legend-scale {
    display: flex;
    gap: 4px;
  }

  .scale-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
  }

  .scale-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .scale-color.level-0 {
    background: var(--el-fill-color-lighter);
  }

  .scale-color.level-1 {
    background: #c6e48b;
  }

  .scale-color.level-2 {
    background: #7bc96f;
  }

  .scale-color.level-3 {
    background: #239a3b;
  }

  .scale-color.level-4 {
    background: #196127;
  }

  .scale-color.level-5 {
    background: #0d2818;
  }

  .heatmap-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 12px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
  }

  .stat-label {
    color: var(--el-text-color-secondary);
  }

  .stat-value {
    color: var(--el-text-color-primary);
    font-weight: 600;
  }

  .heatmap-tooltip {
    position: absolute;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    pointer-events: none;
  }

  .tooltip-content {
    font-size: 12px;
  }

  .tooltip-date {
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }

  .tooltip-value,
  .tooltip-level {
    color: var(--el-text-color-secondary);
    margin-bottom: 2px;
  }

  @media (max-width: 768px) {
    .load-heatmap {
      padding: 8px;
    }

    .heatmap-header {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;
    }

    .heatmap-stats {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .day-number {
      font-size: 8px;
    }

    .indicator-dot {
      width: 3px;
      height: 3px;
    }
  }
</style>
