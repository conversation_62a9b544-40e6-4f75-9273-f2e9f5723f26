<template>
  <div class="load-trend">
    <div class="trend-chart">
      <svg class="trend-svg" :viewBox="`0 0 ${svgWidth} ${svgHeight}`">
        <!-- 网格线 -->
        <defs>
          <pattern id="trendGrid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f5f5f5" stroke-width="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#trendGrid)" />

        <!-- Y轴刻度线 -->
        <g class="y-axis">
          <line
            v-for="tick in yAxisTicks"
            :key="tick"
            :x1="0"
            :y1="getYPosition(tick)"
            :x2="svgWidth"
            :y2="getYPosition(tick)"
            stroke="#e0e0e0"
            stroke-width="1"
            stroke-dasharray="2,2"
          />
          <text
            v-for="tick in yAxisTicks"
            :key="`label-${tick}`"
            :x="5"
            :y="getYPosition(tick) - 5"
            font-size="10"
            fill="#999"
          >
            {{ tick }}min
          </text>
        </g>

        <!-- 历史数据区域 -->
        <path
          :d="historicalAreaPath"
          fill="url(#historicalGradient)"
          opacity="0.3"
        />

        <!-- 历史数据线 -->
        <path
          :d="historicalLinePath"
          fill="none"
          stroke="var(--el-color-primary)"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <!-- 预测数据区域 -->
        <path
          :d="predictedAreaPath"
          fill="url(#predictedGradient)"
          opacity="0.2"
        />

        <!-- 预测数据线 -->
        <path
          :d="predictedLinePath"
          fill="none"
          stroke="var(--el-color-warning)"
          stroke-width="2"
          stroke-dasharray="5,5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <!-- 数据点 -->
        <circle
          v-for="(point, index) in allPoints"
          :key="index"
          :cx="point.x"
          :cy="point.y"
          :r="point.predicted ? 3 : 4"
          :fill="point.predicted ? 'var(--el-color-warning)' : 'var(--el-color-primary)'"
          stroke="white"
          stroke-width="2"
          class="data-point"
          @mouseenter="showTooltip($event, data[index])"
          @mouseleave="hideTooltip"
        />

        <!-- 趋势线 -->
        <path
          v-if="trendLine"
          :d="trendLine"
          fill="none"
          stroke="var(--el-color-success)"
          stroke-width="1"
          stroke-dasharray="3,3"
          opacity="0.7"
        />

        <!-- 渐变定义 -->
        <defs>
          <linearGradient id="historicalGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:var(--el-color-primary);stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:var(--el-color-primary);stop-opacity:0.1" />
          </linearGradient>
          <linearGradient id="predictedGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:var(--el-color-warning);stop-opacity:0.6" />
            <stop offset="100%" style="stop-color:var(--el-color-warning);stop-opacity:0.1" />
          </linearGradient>
        </defs>
      </svg>

      <!-- X轴标签 -->
      <div class="x-axis">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="x-label"
          :class="{ predicted: item.predicted }"
          :style="{ left: `${(index / (data.length - 1)) * 100}%` }"
        >
          {{ item.date }}
        </div>
      </div>
    </div>

    <!-- 预测信息 -->
    <div class="prediction-info">
      <div class="prediction-card">
        <div class="prediction-header">
          <h5>趋势预测</h5>
          <el-tag :type="getTrendType(predictions.trend)" size="small">
            {{ getTrendText(predictions.trend) }}
          </el-tag>
        </div>
        
        <div class="prediction-content">
          <div class="prediction-item">
            <span class="label">平均负载:</span>
            <span class="value">{{ predictions.averageLoad }}分钟</span>
          </div>
          <div class="prediction-item">
            <span class="label">预测准确度:</span>
            <span class="value">{{ Math.round(predictions.confidence * 100) }}%</span>
          </div>
          <div class="prediction-item">
            <span class="label">趋势方向:</span>
            <span class="value" :class="getTrendClass(predictions.trend)">
              {{ getTrendDescription(predictions.trend) }}
            </span>
          </div>
        </div>

        <div class="prediction-suggestions">
          <h6>建议:</h6>
          <ul class="suggestion-list">
            <li v-for="suggestion in getTrendSuggestions(predictions.trend)" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 图例 -->
    <div class="trend-legend">
      <div class="legend-item">
        <div class="legend-line historical"></div>
        <span>历史数据</span>
      </div>
      <div class="legend-item">
        <div class="legend-line predicted"></div>
        <span>预测数据</span>
      </div>
      <div class="legend-item">
        <div class="legend-line trend"></div>
        <span>趋势线</span>
      </div>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="tooltip.visible"
      class="trend-tooltip"
      :style="{ 
        left: `${tooltip.x}px`, 
        top: `${tooltip.y}px` 
      }"
    >
      <div class="tooltip-content">
        <div class="tooltip-date">{{ tooltip.data?.date }}</div>
        <div class="tooltip-value">
          学习时长: {{ tooltip.data?.value }}分钟
        </div>
        <div class="tooltip-type">
          {{ tooltip.data?.predicted ? '预测数据' : '历史数据' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive } from 'vue'

  interface TrendData {
    date: string
    value: number
    predicted: boolean
  }

  interface Predictions {
    averageLoad: number
    trend: 'increasing' | 'decreasing' | 'stable'
    confidence: number
  }

  interface Props {
    data: TrendData[]
    predictions: Predictions
  }

  const props = defineProps<Props>()

  // 响应式数据
  const tooltip = reactive({
    visible: false,
    x: 0,
    y: 0,
    data: null as TrendData | null
  })

  const svgWidth = 400
  const svgHeight = 200

  // 计算属性
  const maxValue = computed(() => {
    const max = Math.max(...props.data.map(item => item.value))
    return Math.max(max, 60) // 最小显示60分钟
  })

  const yAxisTicks = computed(() => {
    const max = maxValue.value
    const tickCount = 4
    const step = Math.ceil(max / tickCount / 10) * 10
    const ticks = []
    
    for (let i = 0; i <= tickCount; i++) {
      ticks.push(i * step)
    }
    
    return ticks.filter(tick => tick <= max)
  })

  const allPoints = computed(() => {
    return props.data.map((item, index) => ({
      x: (index / (props.data.length - 1)) * svgWidth,
      y: getYPosition(item.value),
      predicted: item.predicted
    }))
  })

  const historicalPoints = computed(() => {
    return allPoints.value.filter((_, index) => !props.data[index].predicted)
  })

  const predictedPoints = computed(() => {
    return allPoints.value.filter((_, index) => props.data[index].predicted)
  })

  const historicalLinePath = computed(() => {
    if (historicalPoints.value.length === 0) {return ''}
    
    let path = `M ${historicalPoints.value[0].x} ${historicalPoints.value[0].y}`
    
    for (let i = 1; i < historicalPoints.value.length; i++) {
      const point = historicalPoints.value[i]
      path += ` L ${point.x} ${point.y}`
    }
    
    return path
  })

  const predictedLinePath = computed(() => {
    if (predictedPoints.value.length === 0) {return ''}
    
    // 连接最后一个历史点和第一个预测点
    const lastHistorical = historicalPoints.value[historicalPoints.value.length - 1]
    const firstPredicted = predictedPoints.value[0]
    
    let path = `M ${lastHistorical.x} ${lastHistorical.y}`
    path += ` L ${firstPredicted.x} ${firstPredicted.y}`
    
    for (let i = 1; i < predictedPoints.value.length; i++) {
      const point = predictedPoints.value[i]
      path += ` L ${point.x} ${point.y}`
    }
    
    return path
  })

  const historicalAreaPath = computed(() => {
    if (historicalPoints.value.length === 0) {return ''}
    
    let path = `M ${historicalPoints.value[0].x} ${svgHeight}`
    path += ` L ${historicalPoints.value[0].x} ${historicalPoints.value[0].y}`
    
    for (let i = 1; i < historicalPoints.value.length; i++) {
      const point = historicalPoints.value[i]
      path += ` L ${point.x} ${point.y}`
    }
    
    path += ` L ${historicalPoints.value[historicalPoints.value.length - 1].x} ${svgHeight}`
    path += ' Z'
    
    return path
  })

  const predictedAreaPath = computed(() => {
    if (predictedPoints.value.length === 0) {return ''}
    
    const lastHistorical = historicalPoints.value[historicalPoints.value.length - 1]
    
    let path = `M ${lastHistorical.x} ${svgHeight}`
    path += ` L ${lastHistorical.x} ${lastHistorical.y}`
    
    for (const point of predictedPoints.value) {
      path += ` L ${point.x} ${point.y}`
    }
    
    path += ` L ${predictedPoints.value[predictedPoints.value.length - 1].x} ${svgHeight}`
    path += ' Z'
    
    return path
  })

  const trendLine = computed(() => {
    if (allPoints.value.length < 2) {return ''}
    
    // 简单线性回归计算趋势线
    const n = allPoints.value.length
    const sumX = allPoints.value.reduce((sum, point) => sum + point.x, 0)
    const sumY = allPoints.value.reduce((sum, point) => sum + point.y, 0)
    const sumXY = allPoints.value.reduce((sum, point) => sum + point.x * point.y, 0)
    const sumXX = allPoints.value.reduce((sum, point) => sum + point.x * point.x, 0)
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    const intercept = (sumY - slope * sumX) / n
    
    const startX = 0
    const endX = svgWidth
    const startY = slope * startX + intercept
    const endY = slope * endX + intercept
    
    return `M ${startX} ${startY} L ${endX} ${endY}`
  })

  // 方法
  const getYPosition = (value: number): number => {
    return svgHeight - (value / maxValue.value) * svgHeight
  }

  const getTrendType = (trend: string): string => {
    switch (trend) {
      case 'increasing': return 'danger'
      case 'decreasing': return 'success'
      default: return 'info'
    }
  }

  const getTrendText = (trend: string): string => {
    switch (trend) {
      case 'increasing': return '上升'
      case 'decreasing': return '下降'
      default: return '稳定'
    }
  }

  const getTrendClass = (trend: string): string => {
    switch (trend) {
      case 'increasing': return 'trend-up'
      case 'decreasing': return 'trend-down'
      default: return 'trend-stable'
    }
  }

  const getTrendDescription = (trend: string): string => {
    switch (trend) {
      case 'increasing': return '负载呈上升趋势'
      case 'decreasing': return '负载呈下降趋势'
      default: return '负载保持稳定'
    }
  }

  const getTrendSuggestions = (trend: string): string[] => {
    switch (trend) {
      case 'increasing':
        return [
          '注意控制学习负载增长',
          '适当调整任务安排',
          '增加休息时间'
        ]
      case 'decreasing':
        return [
          '可以适当增加学习任务',
          '保持当前良好趋势',
          '考虑提高学习强度'
        ]
      default:
        return [
          '保持当前学习节奏',
          '定期评估学习效果',
          '适时调整学习计划'
        ]
    }
  }

  const showTooltip = (event: MouseEvent, data: TrendData) => {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    tooltip.visible = true
    tooltip.x = event.clientX - rect.left + 10
    tooltip.y = event.clientY - rect.top - 10
    tooltip.data = data
  }

  const hideTooltip = () => {
    tooltip.visible = false
    tooltip.data = null
  }
</script>

<style scoped>
  .load-trend {
    padding: 16px;
  }

  .trend-chart {
    position: relative;
    height: 240px;
    margin-bottom: 20px;
  }

  .trend-svg {
    width: 100%;
    height: 200px;
  }

  .data-point {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .data-point:hover {
    r: 6;
  }

  .x-axis {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
  }

  .x-label {
    position: absolute;
    transform: translateX(-50%);
    font-size: 12px;
    color: var(--el-text-color-secondary);
    line-height: 40px;
  }

  .x-label.predicted {
    color: var(--el-color-warning);
    font-style: italic;
  }

  .prediction-info {
    margin-bottom: 20px;
  }

  .prediction-card {
    background: var(--el-fill-color-light);
    border-radius: 8px;
    padding: 16px;
  }

  .prediction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .prediction-header h5 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .prediction-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
  }

  .prediction-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .prediction-item .label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .prediction-item .value {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .trend-up {
    color: var(--el-color-danger);
  }

  .trend-down {
    color: var(--el-color-success);
  }

  .trend-stable {
    color: var(--el-color-info);
  }

  .prediction-suggestions h6 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
    font-size: 14px;
  }

  .suggestion-list {
    margin: 0;
    padding-left: 16px;
    color: var(--el-text-color-regular);
    font-size: 12px;
  }

  .suggestion-list li {
    margin-bottom: 4px;
  }

  .trend-legend {
    display: flex;
    justify-content: center;
    gap: 24px;
    padding: 12px 0;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .legend-line {
    width: 20px;
    height: 2px;
  }

  .legend-line.historical {
    background: var(--el-color-primary);
  }

  .legend-line.predicted {
    background: var(--el-color-warning);
    background-image: repeating-linear-gradient(
      to right,
      transparent,
      transparent 3px,
      var(--el-color-warning) 3px,
      var(--el-color-warning) 6px
    );
  }

  .legend-line.trend {
    background: var(--el-color-success);
    background-image: repeating-linear-gradient(
      to right,
      transparent,
      transparent 2px,
      var(--el-color-success) 2px,
      var(--el-color-success) 4px
    );
  }

  .trend-tooltip {
    position: absolute;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    pointer-events: none;
  }

  .tooltip-content {
    font-size: 12px;
  }

  .tooltip-date {
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }

  .tooltip-value,
  .tooltip-type {
    color: var(--el-text-color-secondary);
    margin-bottom: 2px;
  }

  @media (max-width: 768px) {
    .load-trend {
      padding: 8px;
    }

    .prediction-content {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .trend-legend {
      flex-direction: column;
      gap: 8px;
    }
  }
</style>
