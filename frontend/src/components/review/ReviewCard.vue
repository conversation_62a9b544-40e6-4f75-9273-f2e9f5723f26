<template>
  <div class="review-card" :class="cardClass">
    <div class="card-header">
      <div class="task-info">
        <h3 class="task-title">{{ reviewPlan.task.title }}</h3>
        <el-tag :color="subjectColor" size="small">
          {{ getSubjectName(reviewPlan.task.subject) }}
        </el-tag>
      </div>
      <div class="progress-info">
        <span class="progress-text">{{ reviewPlan.completedCount }}/{{ reviewPlan.totalCount }}</span>
        <el-progress 
          :percentage="progressPercentage" 
          :stroke-width="6"
          :show-text="false"
          :color="progressColor"
        />
      </div>
    </div>

    <div class="card-body">
      <p class="task-content">{{ reviewPlan.task.content }}</p>
      
      <div class="review-stats">
        <div class="stat-item">
          <el-icon><Clock /></el-icon>
          <span>开始学习：{{ formatDate(reviewPlan.startTime) }}</span>
        </div>
        
        <div v-if="nextReview" class="stat-item next-review">
          <el-icon><Bell /></el-icon>
          <span>下次复习：{{ formatDateTime(nextReview.scheduledTime) }}</span>
          <el-tag 
            v-if="isOverdue(nextReview)" 
            type="danger" 
            size="small"
          >
            已逾期
          </el-tag>
        </div>

        <div v-if="averageRating > 0" class="stat-item">
          <el-icon><Star /></el-icon>
          <span>平均评分：</span>
          <el-rate 
            v-model="averageRating" 
            disabled 
            size="small"
            :colors="['#F56C6C', '#E6A23C', '#67C23A']"
          />
          <span class="rating-value">({{ averageRating.toFixed(1) }})</span>
        </div>

        <div v-if="totalStudyTime > 0" class="stat-item">
          <el-icon><Timer /></el-icon>
          <span>累计用时：{{ totalStudyTime }}分钟</span>
        </div>
      </div>

      <div class="review-tags">
        <el-tag 
          v-for="tag in reviewPlan.task.tags" 
          :key="tag"
          size="small"
          type="info"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <div class="card-footer">
      <div class="status-indicator">
        <el-icon v-if="reviewPlan.isCompleted" class="status-icon completed">
          <CircleCheck />
        </el-icon>
        <el-icon v-else-if="hasOverdueReviews" class="status-icon overdue">
          <Warning />
        </el-icon>
        <el-icon v-else class="status-icon active">
          <Clock />
        </el-icon>
        <span class="status-text">{{ statusText }}</span>
      </div>

      <div class="card-actions">
        <el-button 
          v-if="!reviewPlan.isCompleted"
          type="primary" 
          size="small"
          @click="$emit('start-review', reviewPlan)"
        >
          {{ nextReview ? '开始复习' : '查看详情' }}
        </el-button>
        
        <el-button 
          size="small"
          @click="$emit('view-timeline', reviewPlan)"
        >
          查看时间轴
        </el-button>

        <el-dropdown @command="handleCommand">
          <el-button size="small" type="info" :icon="More" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="history">复习历史</el-dropdown-item>
              <el-dropdown-item command="edit">编辑任务</el-dropdown-item>
              <el-dropdown-item command="reset" divided>重置进度</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { 
    Bell, 
    CircleCheck, 
    Clock, 
    More, 
    Star, 
    Timer, 
    Warning 
  } from '@element-plus/icons-vue'
  import type { ReviewPlan, ReviewRecord } from '@/types'

  interface Props {
    reviewPlan: ReviewPlan
  }

  interface Emits {
    (e: 'start-review', plan: ReviewPlan): void
    (e: 'view-timeline', plan: ReviewPlan): void
    (e: 'view-history', plan: ReviewPlan): void
    (e: 'edit-task', plan: ReviewPlan): void
    (e: 'reset-progress', plan: ReviewPlan): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const progressPercentage = computed(() => {
    return Math.round((props.reviewPlan.completedCount / props.reviewPlan.totalCount) * 100)
  })

  const progressColor = computed(() => {
    const percentage = progressPercentage.value
    if (percentage >= 80) {return '#67C23A'}
    if (percentage >= 50) {return '#E6A23C'}
    return '#F56C6C'
  })

  const nextReview = computed((): ReviewRecord | undefined => {
    return props.reviewPlan.reviews.find(r => r.status === 'scheduled')
  })

  const hasOverdueReviews = computed(() => {
    const now = new Date()
    return props.reviewPlan.reviews.some(r => 
      r.status === 'scheduled' && new Date(r.scheduledTime) < now
    )
  })

  const averageRating = computed(() => {
    const completedReviews = props.reviewPlan.reviews.filter(r => r.rating)
    if (completedReviews.length === 0) {return 0}
    
    const totalRating = completedReviews.reduce((sum, r) => sum + (r.rating || 0), 0)
    return totalRating / completedReviews.length
  })

  const totalStudyTime = computed(() => {
    return props.reviewPlan.reviews.reduce((total, r) => total + (r.duration || 0), 0)
  })

  const statusText = computed(() => {
    if (props.reviewPlan.isCompleted) {return '复习完成'}
    if (hasOverdueReviews.value) {return '有逾期复习'}
    return '复习进行中'
  })

  const cardClass = computed(() => {
    const classes = ['review-card']
    if (props.reviewPlan.isCompleted) {classes.push('completed')}
    else if (hasOverdueReviews.value) {classes.push('overdue')}
    else {classes.push('active')}
    return classes.join(' ')
  })

  const subjectColor = computed(() => {
    const colorMap: Record<string, string> = {
      chinese: '#E74C3C',
      math: '#3498DB',
      english: '#9B59B6',
      physics: '#E67E22',
      chemistry: '#1ABC9C',
      biology: '#27AE60',
      history: '#F39C12',
      geography: '#34495E'
    }
    return colorMap[props.reviewPlan.task.subject] || '#95A5A6'
  })

  const getSubjectName = (subject: string): string => {
    const nameMap: Record<string, string> = {
      chinese: '语文',
      math: '数学',
      english: '英语',
      physics: '物理',
      chemistry: '化学',
      biology: '生物',
      history: '历史',
      geography: '地理'
    }
    return nameMap[subject] || subject
  }

  const isOverdue = (review: ReviewRecord): boolean => {
    return new Date(review.scheduledTime) < new Date()
  }

  const formatDate = (dateStr: string): string => {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  }

  const formatDateTime = (dateStr: string): string => {
    return new Date(dateStr).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleCommand = (command: string) => {
    switch (command) {
      case 'history':
        emit('view-history', props.reviewPlan)
        break
      case 'edit':
        emit('edit-task', props.reviewPlan)
        break
      case 'reset':
        emit('reset-progress', props.reviewPlan)
        break
    }
  }
</script>

<style scoped>
  .review-card {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .review-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .review-card.completed {
    border-left: 4px solid var(--el-color-success);
  }

  .review-card.overdue {
    border-left: 4px solid var(--el-color-danger);
  }

  .review-card.active {
    border-left: 4px solid var(--el-color-primary);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .task-info {
    flex: 1;
  }

  .task-title {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
    font-size: 18px;
    font-weight: 600;
  }

  .progress-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    min-width: 120px;
  }

  .progress-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-bottom: 4px;
  }

  .task-content {
    color: var(--el-text-color-regular);
    line-height: 1.6;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .review-stats {
    margin-bottom: 16px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .stat-item.next-review {
    color: var(--el-color-primary);
    font-weight: 500;
  }

  .rating-value {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }

  .review-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-icon {
    font-size: 16px;
  }

  .status-icon.completed {
    color: var(--el-color-success);
  }

  .status-icon.overdue {
    color: var(--el-color-danger);
  }

  .status-icon.active {
    color: var(--el-color-primary);
  }

  .status-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .card-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  @media (max-width: 768px) {
    .card-header {
      flex-direction: column;
      gap: 12px;
    }

    .progress-info {
      align-items: flex-start;
      width: 100%;
    }

    .card-footer {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .card-actions {
      justify-content: center;
    }
  }
</style>
