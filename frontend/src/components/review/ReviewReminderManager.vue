<template>
  <div class="review-reminder-manager">
    <ElCard class="settings-card">
      <template #header>
        <div class="card-header">
          <ElIcon><Bell /></ElIcon>
          <span>复习提醒设置</span>
        </div>
      </template>
      
      <div class="settings-content">
        <!-- 基础设置 -->
        <div class="setting-group">
          <h4>基础设置</h4>
          
          <div class="setting-item">
            <div class="setting-label">
              <span>启用复习提醒</span>
              <ElTooltip content="开启后将在复习时间到达时发送提醒" placement="top">
                <ElIcon class="help-icon"><QuestionFilled /></ElIcon>
              </ElTooltip>
            </div>
            <ElSwitch
              v-model="localSettings.enabled"
              @change="saveSettings"
            />
          </div>
          
          <div class="setting-item">
            <div class="setting-label">浏览器通知</div>
            <ElSwitch
              v-model="localSettings.browserNotifications"
              :disabled="!localSettings.enabled"
              @change="handleBrowserNotificationChange"
            />
          </div>
          
          <div class="setting-item">
            <div class="setting-label">应用内消息</div>
            <ElSwitch
              v-model="localSettings.inAppMessages"
              :disabled="!localSettings.enabled"
              @change="saveSettings"
            />
          </div>
          
          <div class="setting-item">
            <div class="setting-label">提醒音效</div>
            <ElSwitch
              v-model="localSettings.sound"
              :disabled="!localSettings.enabled"
              @change="saveSettings"
            />
          </div>
        </div>
        
        <!-- 免打扰时间 -->
        <div class="setting-group">
          <h4>免打扰时间</h4>
          
          <div class="setting-item">
            <div class="setting-label">启用免打扰</div>
            <ElSwitch
              v-model="localSettings.quietHours.enabled"
              :disabled="!localSettings.enabled"
              @change="saveSettings"
            />
          </div>
          
          <div v-if="localSettings.quietHours.enabled" class="time-range">
            <div class="setting-item">
              <div class="setting-label">开始时间</div>
              <ElTimePicker
                v-model="quietStartTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择开始时间"
                @change="updateQuietHours"
              />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">结束时间</div>
              <ElTimePicker
                v-model="quietEndTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择结束时间"
                @change="updateQuietHours"
              />
            </div>
          </div>
        </div>
        
        <!-- 提醒频率 -->
        <div class="setting-group">
          <h4>提醒频率</h4>
          
          <div class="setting-item">
            <div class="setting-label">
              <span>复习前提醒</span>
              <span class="setting-desc">提前多少分钟提醒</span>
            </div>
            <ElInputNumber
              v-model="localSettings.frequency.beforeReview"
              :min="0"
              :max="60"
              :step="5"
              :disabled="!localSettings.enabled"
              @change="saveSettings"
            />
            <span class="unit">分钟</span>
          </div>
          
          <div class="setting-item">
            <div class="setting-label">
              <span>逾期重复提醒</span>
              <span class="setting-desc">逾期后多久再次提醒</span>
            </div>
            <ElInputNumber
              v-model="localSettings.frequency.overdue"
              :min="10"
              :max="120"
              :step="10"
              :disabled="!localSettings.enabled"
              @change="saveSettings"
            />
            <span class="unit">分钟</span>
          </div>
        </div>
        
        <!-- 测试按钮 -->
        <div class="setting-group">
          <h4>测试功能</h4>
          
          <div class="test-buttons">
            <ElButton
              type="primary"
              :disabled="!localSettings.enabled"
              @click="testBrowserNotification"
            >
              测试浏览器通知
            </ElButton>
            
            <ElButton
              type="success"
              :disabled="!localSettings.enabled"
              @click="testInAppMessage"
            >
              测试应用内消息
            </ElButton>
          </div>
        </div>
      </div>
    </ElCard>
    
    <!-- 权限提示 -->
    <ElAlert
      v-if="showPermissionAlert"
      title="需要通知权限"
      type="warning"
      :closable="false"
      show-icon
      class="permission-alert"
    >
      <template #default>
        <p>浏览器通知需要您的授权才能正常工作。</p>
        <ElButton
          type="primary"
          size="small"
          @click="requestPermission"
        >
          授权通知权限
        </ElButton>
      </template>
    </ElAlert>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { ElAlert, ElButton, ElCard, ElIcon, ElInputNumber, ElMessage, ElSwitch, ElTimePicker, ElTooltip } from 'element-plus'
import { Bell, QuestionFilled } from '@element-plus/icons-vue'
import NotificationService, { type ReminderSettings } from '@/services/NotificationService'

const notificationService = NotificationService.getInstance()

// 本地设置状态
const localSettings = reactive<ReminderSettings>({
  enabled: true,
  browserNotifications: true,
  inAppMessages: true,
  sound: true,
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '08:00'
  },
  frequency: {
    beforeReview: 5,
    overdue: 30
  }
})

// 时间选择器的值
const quietStartTime = ref('')
const quietEndTime = ref('')

// 权限提示
const showPermissionAlert = ref(false)

// 计算属性
const hasNotificationPermission = computed(() => {
  return 'Notification' in window && Notification.permission === 'granted'
})

onMounted(() => {
  loadSettings()
  checkNotificationPermission()
})

/**
 * 加载设置
 */
const loadSettings = () => {
  const settings = notificationService.getSettings()
  Object.assign(localSettings, settings)
  
  // 更新时间选择器的值
  quietStartTime.value = localSettings.quietHours.start
  quietEndTime.value = localSettings.quietHours.end
}

/**
 * 保存设置
 */
const saveSettings = () => {
  notificationService.saveSettings(localSettings)
  ElMessage.success('设置已保存')
}

/**
 * 检查通知权限
 */
const checkNotificationPermission = () => {
  if (localSettings.browserNotifications && !hasNotificationPermission.value) {
    showPermissionAlert.value = true
  } else {
    showPermissionAlert.value = false
  }
}

/**
 * 处理浏览器通知开关变化
 */
const handleBrowserNotificationChange = async () => {
  if (localSettings.browserNotifications) {
    const hasPermission = await notificationService.requestBrowserPermission()
    if (!hasPermission) {
      localSettings.browserNotifications = false
      ElMessage.warning('未获得通知权限，已关闭浏览器通知')
    }
  }
  
  checkNotificationPermission()
  saveSettings()
}

/**
 * 请求通知权限
 */
const requestPermission = async () => {
  const hasPermission = await notificationService.requestBrowserPermission()
  if (hasPermission) {
    showPermissionAlert.value = false
    ElMessage.success('通知权限已授权')
  } else {
    ElMessage.error('通知权限被拒绝')
  }
}

/**
 * 更新免打扰时间
 */
const updateQuietHours = () => {
  if (quietStartTime.value) {
    localSettings.quietHours.start = quietStartTime.value
  }
  if (quietEndTime.value) {
    localSettings.quietHours.end = quietEndTime.value
  }
  saveSettings()
}

/**
 * 测试浏览器通知
 */
const testBrowserNotification = () => {
  notificationService.sendBrowserNotification({
    title: '复习提醒测试',
    body: '这是一条测试通知，如果您看到这条消息，说明浏览器通知功能正常工作。',
    tag: 'test-notification'
  })
}

/**
 * 测试应用内消息
 */
const testInAppMessage = () => {
  notificationService.sendInAppMessage({
    type: 'info',
    title: '应用内消息测试',
    message: '这是一条测试消息，如果您看到这条消息，说明应用内通知功能正常工作。',
    duration: 5000,
    actions: [
      {
        text: '知道了',
        action: () => {
          ElMessage.success('测试完成')
        }
      }
    ]
  })
}
</script>

<style scoped lang="scss">
.review-reminder-manager {
  max-width: 600px;
  margin: 0 auto;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.settings-content {
  .setting-group {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
    }
  }
  
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .setting-label {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
    
    .setting-desc {
      font-size: 12px;
      color: #909399;
    }
    
    .help-icon {
      margin-left: 4px;
      color: #909399;
      cursor: help;
    }
  }
  
  .unit {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }
  
  .time-range {
    margin-left: 20px;
    padding-left: 16px;
    border-left: 2px solid #ebeef5;
  }
  
  .test-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

.permission-alert {
  margin-top: 16px;
  
  p {
    margin: 0 0 8px 0;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .review-reminder-manager {
    max-width: none;
    margin: 0;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .test-buttons {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
  
  .time-range {
    margin-left: 0;
    padding-left: 0;
    border-left: none;
    padding-top: 8px;
  }
}
</style>
