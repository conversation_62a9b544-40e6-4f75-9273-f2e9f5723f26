<template>
  <div class="review-timeline">
    <div class="timeline-header">
      <h3>艾宾浩斯复习时间轴</h3>
      <p class="timeline-desc">根据记忆遗忘曲线安排的9个复习时间点</p>
    </div>

    <div class="timeline-container">
      <div class="timeline-line"></div>
      
      <div 
        v-for="review in reviews"
        :key="review.id"
        class="timeline-item"
        :class="getTimelineItemClass(review)"
      >
        <!-- 时间点圆圈 -->
        <div class="timeline-dot">
          <el-icon v-if="review.status === 'completed'" class="status-icon completed">
            <Check />
          </el-icon>
          <el-icon v-else-if="review.status === 'overdue'" class="status-icon overdue">
            <Close />
          </el-icon>
          <el-icon v-else-if="review.status === 'skipped'" class="status-icon skipped">
            <Minus />
          </el-icon>
          <span v-else class="timeline-number">{{ review.intervalId }}</span>
        </div>

        <!-- 时间点内容 -->
        <div class="timeline-content">
          <div class="timeline-card">
            <div class="card-header">
              <h4>{{ getIntervalName(review.intervalId) }}</h4>
              <el-tag 
                :type="getStatusTagType(review.status)" 
                size="small"
              >
                {{ getStatusText(review.status) }}
              </el-tag>
            </div>

            <div class="card-body">
              <div class="time-info">
                <el-icon><Clock /></el-icon>
                <span>计划时间：{{ formatTime(review.scheduledTime) }}</span>
              </div>
              
              <div v-if="review.actualTime" class="time-info">
                <el-icon><Check /></el-icon>
                <span>完成时间：{{ formatTime(review.actualTime) }}</span>
              </div>

              <div v-if="review.rating" class="rating-info">
                <span class="rating-label">复习效果：</span>
                <el-rate 
                  v-model="review.rating" 
                  disabled 
                  size="small"
                  :colors="['#F56C6C', '#E6A23C', '#67C23A']"
                />
                <span class="rating-text">{{ getRatingText(review.rating) }}</span>
              </div>

              <div v-if="review.duration" class="duration-info">
                <el-icon><Timer /></el-icon>
                <span>用时：{{ review.duration }}分钟</span>
              </div>

              <div v-if="review.notes" class="notes-info">
                <p class="notes-text">{{ review.notes }}</p>
              </div>
            </div>

            <div v-if="review.status === 'scheduled'" class="card-actions">
              <el-button 
                type="primary" 
                size="small"
                @click="$emit('start-review', review)"
              >
                开始复习
              </el-button>
              <el-button 
                size="small"
                @click="$emit('skip-review', review)"
              >
                跳过
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { } from 'vue'
  import { Check, Clock, Close, Minus, Timer } from '@element-plus/icons-vue'
  import type { ReviewRating, ReviewRecord, ReviewStatus } from '@/types'
  import { EBBINGHAUS_INTERVALS } from '@/types'

  interface Props {
    reviews: ReviewRecord[]
  }

  interface Emits {
    (e: 'start-review', review: ReviewRecord): void
    (e: 'skip-review', review: ReviewRecord): void
  }

  defineProps<Props>()
  defineEmits<Emits>()

  const getIntervalName = (intervalId: number): string => {
    const interval = EBBINGHAUS_INTERVALS.find(i => i.id === intervalId)
    return interval ? interval.name : `第${intervalId}次复习`
  }

  const getTimelineItemClass = (review: ReviewRecord): string => {
    const classes = ['timeline-item']
    classes.push(`status-${review.status}`)
    
    // 添加当前时间点标识
    const now = new Date()
    const reviewTime = new Date(review.scheduledTime)
    if (review.status === 'scheduled' && Math.abs(now.getTime() - reviewTime.getTime()) < 3600000) {
      classes.push('current')
    }
    
    return classes.join(' ')
  }

  const getStatusTagType = (status: ReviewStatus): string => {
    const typeMap = {
      completed: 'success',
      scheduled: 'info',
      overdue: 'danger',
      skipped: 'warning'
    }
    return typeMap[status] || 'info'
  }

  const getStatusText = (status: ReviewStatus): string => {
    const textMap = {
      completed: '已完成',
      scheduled: '待复习',
      overdue: '已逾期',
      skipped: '已跳过'
    }
    return textMap[status] || '未知'
  }

  const getRatingText = (rating: ReviewRating): string => {
    const textMap = {
      1: '很差',
      2: '较差',
      3: '一般',
      4: '良好',
      5: '优秀'
    }
    return textMap[rating] || ''
  }

  const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
</script>

<style scoped>
  .review-timeline {
    padding: 20px;
  }

  .timeline-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .timeline-header h3 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  .timeline-desc {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
  }

  .timeline-line {
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--el-border-color-light);
    z-index: 1;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 40px;
    display: flex;
    align-items: flex-start;
  }

  .timeline-dot {
    position: relative;
    z-index: 2;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--el-bg-color);
    border: 3px solid var(--el-border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .timeline-number {
    font-weight: bold;
    color: var(--el-text-color-regular);
  }

  .status-icon {
    font-size: 24px;
  }

  .status-icon.completed {
    color: var(--el-color-success);
  }

  .status-icon.overdue {
    color: var(--el-color-danger);
  }

  .status-icon.skipped {
    color: var(--el-color-warning);
  }

  .timeline-item.status-completed .timeline-dot {
    border-color: var(--el-color-success);
    background: var(--el-color-success-light-9);
  }

  .timeline-item.status-overdue .timeline-dot {
    border-color: var(--el-color-danger);
    background: var(--el-color-danger-light-9);
  }

  .timeline-item.status-skipped .timeline-dot {
    border-color: var(--el-color-warning);
    background: var(--el-color-warning-light-9);
  }

  .timeline-item.current .timeline-dot {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    box-shadow: 0 0 0 4px var(--el-color-primary-light-8);
  }

  .timeline-content {
    flex: 1;
    margin-top: 10px;
  }

  .timeline-card {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .card-header h4 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .card-body {
    space-y: 8px;
  }

  .time-info,
  .duration-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin-bottom: 8px;
  }

  .rating-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .rating-label {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .rating-text {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }

  .notes-info {
    margin-top: 12px;
    padding: 8px;
    background: var(--el-fill-color-light);
    border-radius: 4px;
  }

  .notes-text {
    margin: 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
    line-height: 1.5;
  }

  .card-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
  }

  @media (max-width: 768px) {
    .timeline-container {
      padding-left: 20px;
    }
    
    .timeline-line {
      left: 20px;
    }
    
    .timeline-dot {
      width: 40px;
      height: 40px;
      margin-right: 15px;
    }
    
    .timeline-number {
      font-size: 14px;
    }
    
    .status-icon {
      font-size: 18px;
    }
  }
</style>
