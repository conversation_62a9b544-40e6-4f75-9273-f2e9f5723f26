<template>
  <ElDialog
    v-model="visible"
    title="复习执行"
    width="600px"
    :before-close="handleClose"
    class="review-execution-dialog"
  >
    <div v-if="currentReview" class="execution-content">
      <!-- 任务信息 -->
      <div class="task-info">
        <h3>{{ task?.title }}</h3>
        <p class="task-description">{{ task?.content }}</p>
        <div class="review-info">
          <el-tag type="primary">{{ getIntervalName(currentReview.intervalId) }}</el-tag>
          <span class="scheduled-time">
            计划时间：{{ formatDateTime(currentReview.scheduledTime) }}
          </span>
        </div>
      </div>

      <!-- 复习计时器 -->
      <div class="timer-section">
        <div class="timer-display">
          <el-icon class="timer-icon"><Timer /></el-icon>
          <span class="timer-text">{{ formatTime(elapsedTime) }}</span>
        </div>
        <div class="timer-controls">
          <el-button 
            v-if="!isRunning" 
            type="primary" 
            :icon="VideoPlay"
            @click="startTimer"
          >
            开始计时
          </el-button>
          <el-button 
            v-else 
            type="warning" 
            :icon="VideoPause"
            @click="pauseTimer"
          >
            暂停计时
          </el-button>
          <el-button :icon="RefreshRight" @click="resetTimer">
            重置
          </el-button>
        </div>
      </div>

      <!-- 复习效果评分 -->
      <div class="rating-section">
        <h4>复习效果评分</h4>
        <div class="rating-container">
          <el-rate
            v-model="rating"
            :colors="['#F56C6C', '#E6A23C', '#67C23A']"
            size="large"
            show-text
            :texts="ratingTexts"
          />
        </div>
        <p class="rating-description">
          请根据本次复习的掌握程度进行评分，这将影响后续复习计划的调整
        </p>
      </div>

      <!-- 复习笔记 -->
      <div class="notes-section">
        <h4>复习笔记（可选）</h4>
        <el-input
          v-model="notes"
          type="textarea"
          :rows="4"
          placeholder="记录本次复习的心得、难点或需要注意的地方..."
          maxlength="500"
          show-word-limit
        />
      </div>

      <!-- 复习建议 -->
      <div v-if="rating > 0" class="suggestions-section">
        <h4>复习建议</h4>
        <div class="suggestion-card">
          <el-icon class="suggestion-icon"><InfoFilled /></el-icon>
          <p>{{ getSuggestion(rating) }}</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="success"
          :loading="submitting"
          @click="showRatingDialog"
        >
          完成复习
        </el-button>
      </div>
    </template>
  </ElDialog>

  <!-- 评分对话框 -->
  <ElDialog
    v-model="showRating"
    title="复习效果评分"
    width="90%"
    :max-width="600"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <ReviewRating
      :task="task"
      :review-index="currentReview?.intervalId || 0"
      :start-time="reviewStartTime"
      :end-time="reviewEndTime"
      @submit="handleRatingSubmit"
      @cancel="handleRatingCancel"
    />
  </ElDialog>
</template>

<script setup lang="ts">
  import { computed, onUnmounted, ref, watch } from 'vue'
  import { ElDialog, ElMessage, ElMessageBox } from 'element-plus'
  import {
    InfoFilled,
    RefreshRight,
    Timer,
    VideoPause,
    VideoPlay
  } from '@element-plus/icons-vue'
  import type { ReviewRating as ReviewRatingEnum, ReviewRecord, Task } from '@/types'
  import { EBBINGHAUS_INTERVALS } from '@/types'
  import ReviewRating from './ReviewRating.vue'

  interface Props {
    modelValue: boolean
    review?: ReviewRecord
    task?: Task
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'complete', data: {
      quality: number
      duration: number
      notes: string
      difficulty: number
      nextReviewTime: string
    }): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const currentReview = computed(() => props.review)
  const task = computed(() => props.task)

  // 计时器相关
  const elapsedTime = ref(0)
  const isRunning = ref(false)
  const startTime = ref<Date | null>(null)
  const pausedTime = ref(0)
  let timerInterval: number | null = null

  // 评分相关
  const showRating = ref(false)
  const reviewStartTime = ref('')
  const reviewEndTime = ref('')
  const submitting = ref(false)

  // 保留原有的评分和笔记（用于兼容）
  const rating = ref<ReviewRatingEnum>(1)
  const notes = ref('')

  const ratingTexts = ['很差', '较差', '一般', '良好', '优秀']

  // 监听对话框打开/关闭
  watch(visible, (newVal) => {
    if (newVal) {
      resetForm()
    } else {
      stopTimer()
    }
  })

  // 计时器方法
  const startTimer = () => {
    if (!isRunning.value) {
      startTime.value = new Date()
      isRunning.value = true

      // 记录复习开始时间（仅在第一次开始时记录）
      if (!reviewStartTime.value) {
        reviewStartTime.value = new Date().toISOString()
      }

      timerInterval = window.setInterval(() => {
        if (startTime.value) {
          elapsedTime.value = pausedTime.value + Math.floor((Date.now() - startTime.value.getTime()) / 1000)
        }
      }, 1000)
    }
  }

  const pauseTimer = () => {
    if (isRunning.value && timerInterval) {
      clearInterval(timerInterval)
      pausedTime.value = elapsedTime.value
      isRunning.value = false
      timerInterval = null
    }
  }

  const stopTimer = () => {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
    isRunning.value = false
  }

  const resetTimer = () => {
    stopTimer()
    elapsedTime.value = 0
    pausedTime.value = 0
    startTime.value = null
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 其他方法
  const getIntervalName = (intervalId: number): string => {
    const interval = EBBINGHAUS_INTERVALS.find(i => i.id === intervalId)
    return interval ? interval.name : `第${intervalId}次复习`
  }

  const formatDateTime = (dateStr: string): string => {
    return new Date(dateStr).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getSuggestion = (rating: ReviewRating): string => {
    const suggestions = {
      1: '建议重新学习基础概念，加强理解。可以寻求老师或同学的帮助。',
      2: '需要更多练习来巩固知识点，建议增加练习题的数量。',
      3: '基本掌握，建议通过不同类型的题目来加深理解。',
      4: '掌握良好，可以尝试更有挑战性的题目或拓展相关知识。',
      5: '掌握优秀，可以帮助其他同学或探索更深层次的内容。'
    }
    return suggestions[rating] || ''
  }

  const resetForm = () => {
    rating.value = 1
    notes.value = ''
    resetTimer()
  }

  const handleClose = async () => {
    if (isRunning.value || elapsedTime.value > 0 || rating.value > 0 || notes.value.trim()) {
      try {
        await ElMessageBox.confirm(
          '当前有未保存的复习记录，确定要关闭吗？',
          '确认关闭',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        visible.value = false
      } catch {
        // 用户取消关闭
      }
    } else {
      visible.value = false
    }
  }

  // 显示评分对话框
  const showRatingDialog = () => {
    // 停止计时器
    stopTimer()

    // 记录复习结束时间
    reviewEndTime.value = new Date().toISOString()

    // 显示评分对话框
    showRating.value = true
  }

  // 处理评分提交
  const handleRatingSubmit = (ratingData: {
    quality: number
    duration: number
    notes: string
    difficulty: number
    nextReviewTime: string
  }) => {
    // 发送完成事件
    emit('complete', ratingData)

    // 关闭对话框
    showRating.value = false
    visible.value = false

    ElMessage.success('复习完成！')
  }

  // 处理评分取消
  const handleRatingCancel = () => {
    showRating.value = false
    // 重新开始计时器
    startTimer()
  }

  // 组件卸载时清理计时器
  onUnmounted(() => {
    stopTimer()
  })
</script>

<style scoped>
  .review-execution-dialog :deep(.el-dialog__body) {
    padding: 20px;
  }

  .execution-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .task-info {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--el-fill-color-light);
    border-radius: 8px;
  }

  .task-info h3 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
  }

  .task-description {
    color: var(--el-text-color-regular);
    line-height: 1.6;
    margin-bottom: 12px;
  }

  .review-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .scheduled-time {
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  .timer-section {
    margin-bottom: 24px;
    text-align: center;
    padding: 20px;
    background: var(--el-bg-color);
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
  }

  .timer-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;
  }

  .timer-icon {
    font-size: 24px;
    color: var(--el-color-primary);
  }

  .timer-text {
    font-size: 32px;
    font-weight: bold;
    color: var(--el-text-color-primary);
    font-family: 'Courier New', monospace;
  }

  .timer-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
  }

  .rating-section,
  .notes-section,
  .suggestions-section {
    margin-bottom: 24px;
  }

  .rating-section h4,
  .notes-section h4,
  .suggestions-section h4 {
    margin: 0 0 12px 0;
    color: var(--el-text-color-primary);
  }

  .rating-container {
    display: flex;
    justify-content: center;
    margin-bottom: 12px;
  }

  .rating-description {
    color: var(--el-text-color-secondary);
    font-size: 14px;
    text-align: center;
    margin: 0;
  }

  .suggestion-card {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-7);
    border-radius: 8px;
  }

  .suggestion-icon {
    color: var(--el-color-primary);
    font-size: 20px;
    margin-top: 2px;
  }

  .suggestion-card p {
    margin: 0;
    color: var(--el-text-color-primary);
    line-height: 1.6;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  @media (max-width: 768px) {
    .timer-text {
      font-size: 24px;
    }
    
    .timer-controls {
      flex-direction: column;
      align-items: center;
    }
    
    .suggestion-card {
      flex-direction: column;
      gap: 8px;
    }
  }
</style>
