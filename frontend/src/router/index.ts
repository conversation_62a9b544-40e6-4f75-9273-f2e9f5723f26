import type { RouteRecordRaw } from 'vue-router'

// 路由配置
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '学习概览',
      icon: 'House',
      requiresAuth: true
    }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: () => import('@/views/Tasks.vue'),
    meta: {
      title: '任务管理',
      icon: 'Document',
      requiresAuth: true
    }
  },
  {
    path: '/tasks/create',
    name: 'TaskCreate',
    component: () => import('@/views/TaskCreate.vue'),
    meta: {
      title: '创建任务',
      requiresAuth: true
    }
  },
  {
    path: '/tasks/:id',
    name: 'TaskDetail',
    component: () => import('@/views/TaskDetail.vue'),
    meta: {
      title: '任务详情',
      requiresAuth: true
    }
  },
  {
    path: '/review',
    name: 'Review',
    component: () => import('@/views/Review.vue'),
    meta: {
      title: '复习计划',
      icon: 'Clock',
      requiresAuth: true
    }
  },
  {
    path: '/mindmap',
    name: 'MindMap',
    component: () => import('@/views/MindMap.vue'),
    meta: {
      title: '思维导图',
      icon: 'Share',
      requiresAuth: true
    }
  },
  {
    path: '/ebbinghaus-test',
    name: 'EbbinghausTest',
    component: () => import('@/views/EbbinghausTest.vue'),
    meta: {
      title: '艾宾浩斯测试',
      requiresAuth: false
    }
  }
]
