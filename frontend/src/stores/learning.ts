/**
 * 学习数据状态管理
 */

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import TimeEstimationService, { type LearningSession, type UserProfile } from '@/services/TimeEstimationService'

export interface LearningStats {
  totalStudyTime: number
  totalSessions: number
  averageSessionTime: number
  totalTasks: number
  completedTasks: number
  averageRating: number
  streakDays: number
  longestStreak: number
  subjectStats: Record<string, {
    studyTime: number
    sessions: number
    averageRating: number
    efficiency: number
  }>
  weeklyStats: Array<{
    week: string
    studyTime: number
    sessions: number
    averageRating: number
  }>
  monthlyStats: Array<{
    month: string
    studyTime: number
    sessions: number
    averageRating: number
  }>
}

export interface StudyGoal {
  id: string
  type: 'daily' | 'weekly' | 'monthly'
  target: number
  unit: 'minutes' | 'sessions' | 'tasks'
  current: number
  startDate: string
  endDate: string
  completed: boolean
}

export const useLearningStore = defineStore('learning', () => {
  // 状态
  const sessions = ref<LearningSession[]>([])
  const userProfile = ref<UserProfile | null>(null)
  const goals = ref<StudyGoal[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 服务实例
  const estimationService = TimeEstimationService.getInstance()

  // 计算属性
  const totalStudyTime = computed(() => 
    sessions.value.reduce((total, session) => total + session.actualTime, 0)
  )

  const totalSessions = computed(() => sessions.value.length)

  const averageSessionTime = computed(() => {
    if (totalSessions.value === 0) {return 0}
    return Math.round(totalStudyTime.value / totalSessions.value)
  })

  const averageRating = computed(() => {
    if (totalSessions.value === 0) {return 0}
    const sum = sessions.value.reduce((total, session) => total + session.quality, 0)
    return sum / totalSessions.value
  })

  const currentStreak = computed(() => {
    if (sessions.value.length === 0) {return 0}
    
    const today = new Date()
    let streak = 0
    const currentDate = new Date(today)
    
    while (true) {
      const dateStr = currentDate.toDateString()
      const hasSessionOnDate = sessions.value.some(session => 
        new Date(session.timestamp).toDateString() === dateStr
      )
      
      if (hasSessionOnDate) {
        streak++
        currentDate.setDate(currentDate.getDate() - 1)
      } else {
        break
      }
    }
    
    return streak
  })

  const longestStreak = computed(() => {
    if (sessions.value.length === 0) {return 0}
    
    const sessionDates = [...new Set(sessions.value.map(session => 
      new Date(session.timestamp).toDateString()
    ))].sort()
    
    let maxStreak = 0
    let currentStreak = 1
    
    for (let i = 1; i < sessionDates.length; i++) {
      const prevDate = new Date(sessionDates[i - 1])
      const currentDate = new Date(sessionDates[i])
      const diffDays = Math.floor((currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        currentStreak++
      } else {
        maxStreak = Math.max(maxStreak, currentStreak)
        currentStreak = 1
      }
    }
    
    return Math.max(maxStreak, currentStreak)
  })

  const subjectStats = computed(() => {
    const stats: Record<string, {
      studyTime: number
      sessions: number
      averageRating: number
      efficiency: number
    }> = {}
    
    sessions.value.forEach(session => {
      const subject = session.features.subject
      if (!stats[subject]) {
        stats[subject] = {
          studyTime: 0,
          sessions: 0,
          averageRating: 0,
          efficiency: 0
        }
      }
      
      stats[subject].studyTime += session.actualTime
      stats[subject].sessions += 1
      stats[subject].averageRating += session.quality
    })
    
    // 计算平均值和效率
    Object.keys(stats).forEach(subject => {
      const stat = stats[subject]
      stat.averageRating = stat.averageRating / stat.sessions
      
      // 计算效率（预估时间 / 实际时间）
      const subjectSessions = sessions.value.filter(s => s.features.subject === subject)
      const totalEstimated = subjectSessions.reduce((sum, s) => sum + s.features.estimatedTime, 0)
      const totalActual = subjectSessions.reduce((sum, s) => sum + s.actualTime, 0)
      stat.efficiency = totalActual > 0 ? totalEstimated / totalActual : 1
    })
    
    return stats
  })

  const weeklyStats = computed(() => {
    const stats: Record<string, {
      studyTime: number
      sessions: number
      totalRating: number
    }> = {}
    
    sessions.value.forEach(session => {
      const date = new Date(session.timestamp)
      const weekStart = new Date(date)
      weekStart.setDate(date.getDate() - date.getDay())
      const weekKey = weekStart.toISOString().split('T')[0]
      
      if (!stats[weekKey]) {
        stats[weekKey] = {
          studyTime: 0,
          sessions: 0,
          totalRating: 0
        }
      }
      
      stats[weekKey].studyTime += session.actualTime
      stats[weekKey].sessions += 1
      stats[weekKey].totalRating += session.quality
    })
    
    return Object.entries(stats)
      .map(([week, stat]) => ({
        week,
        studyTime: stat.studyTime,
        sessions: stat.sessions,
        averageRating: stat.sessions > 0 ? stat.totalRating / stat.sessions : 0
      }))
      .sort((a, b) => a.week.localeCompare(b.week))
      .slice(-12) // 最近12周
  })

  const monthlyStats = computed(() => {
    const stats: Record<string, {
      studyTime: number
      sessions: number
      totalRating: number
    }> = {}
    
    sessions.value.forEach(session => {
      const date = new Date(session.timestamp)
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`
      
      if (!stats[monthKey]) {
        stats[monthKey] = {
          studyTime: 0,
          sessions: 0,
          totalRating: 0
        }
      }
      
      stats[monthKey].studyTime += session.actualTime
      stats[monthKey].sessions += 1
      stats[monthKey].totalRating += session.quality
    })
    
    return Object.entries(stats)
      .map(([month, stat]) => ({
        month,
        studyTime: stat.studyTime,
        sessions: stat.sessions,
        averageRating: stat.sessions > 0 ? stat.totalRating / stat.sessions : 0
      }))
      .sort((a, b) => a.month.localeCompare(b.month))
      .slice(-12) // 最近12个月
  })

  const todayStats = computed(() => {
    const today = new Date().toDateString()
    const todaySessions = sessions.value.filter(session => 
      new Date(session.timestamp).toDateString() === today
    )
    
    return {
      studyTime: todaySessions.reduce((sum, s) => sum + s.actualTime, 0),
      sessions: todaySessions.length,
      averageRating: todaySessions.length > 0 
        ? todaySessions.reduce((sum, s) => sum + s.quality, 0) / todaySessions.length 
        : 0
    }
  })

  const activeGoals = computed(() => 
    goals.value.filter(goal => !goal.completed && new Date(goal.endDate) >= new Date())
  )

  const completedGoals = computed(() => 
    goals.value.filter(goal => goal.completed)
  )

  // 方法
  const addSession = (session: LearningSession) => {
    sessions.value.push(session)
    
    // 更新时间预估服务
    estimationService.updateLearningHistory(session)
    
    // 更新目标进度
    updateGoalProgress()
  }

  const loadSessions = () => {
    // 从时间预估服务加载会话数据
    // 这里应该有一个方法来获取所有会话
    // sessions.value = estimationService.getAllSessions()
  }

  const loadUserProfile = () => {
    // 从时间预估服务加载用户画像
    // userProfile.value = estimationService.getUserProfile()
  }

  const createGoal = (goal: Omit<StudyGoal, 'id' | 'current' | 'completed'>) => {
    const newGoal: StudyGoal = {
      id: `goal-${Date.now()}`,
      current: 0,
      completed: false,
      ...goal
    }
    
    goals.value.push(newGoal)
    return newGoal
  }

  const updateGoal = (goalId: string, updates: Partial<StudyGoal>) => {
    const goal = goals.value.find(g => g.id === goalId)
    if (goal) {
      Object.assign(goal, updates)
    }
  }

  const deleteGoal = (goalId: string) => {
    const index = goals.value.findIndex(g => g.id === goalId)
    if (index > -1) {
      goals.value.splice(index, 1)
    }
  }

  const updateGoalProgress = () => {
    const now = new Date()
    
    goals.value.forEach(goal => {
      if (goal.completed || new Date(goal.endDate) < now) {return}
      
      let current = 0
      const startDate = new Date(goal.startDate)
      const endDate = new Date(goal.endDate)
      
      const relevantSessions = sessions.value.filter(session => {
        const sessionDate = new Date(session.timestamp)
        return sessionDate >= startDate && sessionDate <= endDate
      })
      
      switch (goal.unit) {
        case 'minutes':
          current = relevantSessions.reduce((sum, s) => sum + s.actualTime, 0)
          break
        case 'sessions':
          current = relevantSessions.length
          break
        case 'tasks':
          current = new Set(relevantSessions.map(s => s.taskId)).size
          break
      }
      
      goal.current = current
      goal.completed = current >= goal.target
    })
  }

  const getAccuracyStats = () => {
    return estimationService.getAccuracyStats()
  }

  const exportLearningData = () => {
    const data = {
      sessions: sessions.value,
      userProfile: userProfile.value,
      goals: goals.value,
      stats: {
        totalStudyTime: totalStudyTime.value,
        totalSessions: totalSessions.value,
        averageRating: averageRating.value,
        currentStreak: currentStreak.value,
        longestStreak: longestStreak.value
      },
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `learning-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const reset = () => {
    sessions.value = []
    userProfile.value = null
    goals.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    sessions,
    userProfile,
    goals,
    loading,
    error,
    
    // 计算属性
    totalStudyTime,
    totalSessions,
    averageSessionTime,
    averageRating,
    currentStreak,
    longestStreak,
    subjectStats,
    weeklyStats,
    monthlyStats,
    todayStats,
    activeGoals,
    completedGoals,
    
    // 方法
    addSession,
    loadSessions,
    loadUserProfile,
    createGoal,
    updateGoal,
    deleteGoal,
    updateGoalProgress,
    getAccuracyStats,
    exportLearningData,
    reset
  }
}, {
  persist: {
    key: 'learning-store',
    storage: localStorage,
    paths: ['sessions', 'goals']
  }
})
