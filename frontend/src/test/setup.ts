import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// 模拟Element Plus组件
vi.mock('element-plus', () => ({
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElCard: { name: 'ElCard', template: '<div class="el-card"><slot /></div>' },
  ElInput: { name: 'ElInput', template: '<input />' },
  ElSelect: { name: 'ElSelect', template: '<select><slot /></select>' },
  ElOption: { name: 'ElOption', template: '<option><slot /></option>' },
  ElTable: { name: 'ElTable', template: '<table><slot /></table>' },
  ElTableColumn: { name: 'ElTableColumn', template: '<td><slot /></td>' },
  ElDialog: { name: 'ElDialog', template: '<div class="el-dialog"><slot /></div>' },
  ElForm: { name: 'ElForm', template: '<form><slot /></form>' },
  ElFormItem: { name: 'ElFormItem', template: '<div class="el-form-item"><slot /></div>' },
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn(),
    alert: vi.fn(),
    prompt: vi.fn()
  },
  ElLoading: {
    service: vi.fn(() => ({
      close: vi.fn()
    }))
  }
}))

// 模拟Element Plus图标
vi.mock('@element-plus/icons-vue', () => ({
  Plus: { name: 'Plus', template: '<i class="icon-plus"></i>' },
  Delete: { name: 'Delete', template: '<i class="icon-delete"></i>' },
  Edit: { name: 'Edit', template: '<i class="icon-edit"></i>' },
  Search: { name: 'Search', template: '<i class="icon-search"></i>' },
  Setting: { name: 'Setting', template: '<i class="icon-setting"></i>' },
  User: { name: 'User', template: '<i class="icon-user"></i>' },
  Calendar: { name: 'Calendar', template: '<i class="icon-calendar"></i>' },
  Clock: { name: 'Clock', template: '<i class="icon-clock"></i>' },
  Star: { name: 'Star', template: '<i class="icon-star"></i>' },
  Flag: { name: 'Flag', template: '<i class="icon-flag"></i>' },
  Check: { name: 'Check', template: '<i class="icon-check"></i>' },
  Close: { name: 'Close', template: '<i class="icon-close"></i>' },
  Warning: { name: 'Warning', template: '<i class="icon-warning"></i>' },
  InfoFilled: { name: 'InfoFilled', template: '<i class="icon-info"></i>' },
  SuccessFilled: { name: 'SuccessFilled', template: '<i class="icon-success"></i>' },
  CircleCheck: { name: 'CircleCheck', template: '<i class="icon-circle-check"></i>' },
  Loading: { name: 'Loading', template: '<i class="icon-loading"></i>' },
  Picture: { name: 'Picture', template: '<i class="icon-picture"></i>' },
  ZoomIn: { name: 'ZoomIn', template: '<i class="icon-zoom-in"></i>' },
  ZoomOut: { name: 'ZoomOut', template: '<i class="icon-zoom-out"></i>' },
  Refresh: { name: 'Refresh', template: '<i class="icon-refresh"></i>' },
  Link: { name: 'Link', template: '<i class="icon-link"></i>' },
  Share: { name: 'Share', template: '<i class="icon-share"></i>' },
  Download: { name: 'Download', template: '<i class="icon-download"></i>' },
  Upload: { name: 'Upload', template: '<i class="icon-upload"></i>' },
  Timer: { name: 'Timer', template: '<i class="icon-timer"></i>' },
  Bell: { name: 'Bell', template: '<i class="icon-bell"></i>' }
}))

// 模拟Vue Router
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }),
  useRoute: () => ({
    path: '/',
    name: 'home',
    params: {},
    query: {},
    meta: {}
  })
}))

// 模拟Pinia stores
vi.mock('@/stores/task', () => ({
  useTaskStore: () => ({
    tasks: [],
    loading: false,
    error: null,
    fetchTasks: vi.fn(),
    createTask: vi.fn(),
    updateTask: vi.fn(),
    deleteTask: vi.fn(),
    getTotalTasks: vi.fn(() => 0),
    getActiveTasks: vi.fn(() => 0),
    getCompletedTasks: vi.fn(() => 0)
  })
}))

vi.mock('@/stores/review', () => ({
  useReviewStore: () => ({
    reviews: [],
    currentReview: null,
    loading: false,
    error: null,
    fetchReviews: vi.fn(),
    startReview: vi.fn(),
    completeReview: vi.fn(),
    getReviewPlans: vi.fn(() => [])
  })
}))

vi.mock('@/stores/app', () => ({
  useAppStore: () => ({
    theme: 'light',
    language: 'zh-CN',
    sidebarCollapsed: false,
    loading: false,
    error: null,
    notifications: [],
    toggleSidebar: vi.fn(),
    setTheme: vi.fn(),
    setLanguage: vi.fn(),
    showNotification: vi.fn(),
    clearNotifications: vi.fn()
  })
}))

// 模拟浏览器API
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
})

// 模拟ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// 模拟IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// 模拟requestAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16))
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id))

// 模拟performance API
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => [])
  }
})

// 模拟localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 模拟sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// 模拟Notification API
Object.defineProperty(window, 'Notification', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    close: vi.fn()
  }))
})

// 设置全局属性
Object.defineProperty(Notification, 'permission', {
  writable: true,
  value: 'granted'
})

Object.defineProperty(Notification, 'requestPermission', {
  writable: true,
  value: vi.fn(() => Promise.resolve('granted'))
})

// 模拟网络连接API
Object.defineProperty(navigator, 'connection', {
  writable: true,
  value: {
    effectiveType: '4g',
    downlink: 10,
    rtt: 100,
    saveData: false
  }
})

// 模拟用户代理
Object.defineProperty(navigator, 'userAgent', {
  writable: true,
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
})

// 配置Vue Test Utils全局属性
config.global.stubs = {
  'router-link': true,
  'router-view': true,
  transition: false,
  'transition-group': false
}

// 全局错误处理
config.global.config.errorHandler = (err) => {
  console.error('Vue error in test:', err)
}

// 模拟console方法以避免测试输出污染
const originalConsole = { ...console }
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}

// 在测试结束后恢复console
afterEach(() => {
  vi.clearAllMocks()
})

// 导出测试工具
export { vi, config }
export const mockLocalStorage = localStorageMock
export const mockSessionStorage = sessionStorageMock
