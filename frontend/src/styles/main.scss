// 全局样式文件
@import './variables.scss';
@import './mixins.scss';

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family:
    'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑',
    Arial, sans-serif;
  font-size: 14px;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
}

#app {
  height: 100%;
}

// 布局样式
.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .app-header {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid var(--el-border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .app-main {
    flex: 1;
    display: flex;
    overflow: hidden;

    .app-sidebar {
      width: 240px;
      background: #fff;
      border-right: 1px solid var(--el-border-color);
      transition: width 0.3s;

      &.collapsed {
        width: 64px;
      }
    }

    .app-content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background: var(--el-bg-color-page);
    }
  }
}

// 任务卡片样式
.task-card {
  margin-bottom: 16px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .task-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }

  .task-content {
    .task-description {
      color: var(--el-text-color-regular);
      line-height: 1.6;
      margin-bottom: 12px;
    }

    .task-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: var(--el-text-color-secondary);

      .subject {
        padding: 2px 8px;
        border-radius: 4px;
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }
    }
  }

  .task-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

// 学科颜色
.subject-chinese {
  background-color: #ff6b6b;
}
.subject-math {
  background-color: #4ecdc4;
}
.subject-english {
  background-color: #45b7d1;
}
.subject-physics {
  background-color: #96ceb4;
}
.subject-chemistry {
  background-color: #ffeaa7;
}
.subject-biology {
  background-color: #dda0dd;
}
.subject-history {
  background-color: #f4a261;
}
.subject-geography {
  background-color: #2a9d8f;
}

// 优先级颜色
.priority-1 {
  color: #c0c4cc;
}
.priority-2 {
  color: #909399;
}
.priority-3 {
  color: #409eff;
}
.priority-4 {
  color: #e6a23c;
}
.priority-5 {
  color: #f56c6c;
}

// 响应式设计
@media (max-width: 768px) {
  .app-layout {
    .app-main {
      .app-sidebar {
        position: fixed;
        left: -240px;
        top: 60px;
        height: calc(100vh - 60px);
        z-index: 999;
        transition: left 0.3s;

        &.mobile-open {
          left: 0;
        }
      }

      .app-content {
        padding: 12px;
      }
    }
  }

  .task-card {
    .task-actions {
      flex-direction: column;
      gap: 4px;
    }
  }
}

// 工具类
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}
.mb-24 {
  margin-bottom: 24px;
}
.mt-16 {
  margin-top: 16px;
}
.mt-24 {
  margin-top: 24px;
}

.flex {
  display: flex;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.full-width {
  width: 100%;
}
.full-height {
  height: 100%;
}
