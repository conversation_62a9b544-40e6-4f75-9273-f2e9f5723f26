/**
 * 性能优化工具函数
 */

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastCallTime = 0

  return function (this: any, ...args: Parameters<T>) {
    const now = Date.now()
    const callNow = immediate && !timeoutId

    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    if (callNow) {
      func.apply(this, args)
      lastCallTime = now
    } else {
      timeoutId = setTimeout(() => {
        if (now - lastCallTime >= delay) {
          func.apply(this, args)
        }
        timeoutId = null
      }, delay)
    }
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @param options 选项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options: {
    leading?: boolean
    trailing?: boolean
  } = {}
): (...args: Parameters<T>) => void {
  const { leading = true, trailing = true } = options
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastCallTime = 0
  let lastArgs: Parameters<T> | null = null

  return function (this: any, ...args: Parameters<T>) {
    const now = Date.now()
    const timeSinceLastCall = now - lastCallTime

    lastArgs = args

    if (timeSinceLastCall >= delay) {
      if (leading) {
        func.apply(this, args)
        lastCallTime = now
      }
    } else if (trailing && !timeoutId) {
      timeoutId = setTimeout(() => {
        if (lastArgs && trailing) {
          func.apply(this, lastArgs)
          lastCallTime = Date.now()
        }
        timeoutId = null
      }, delay - timeSinceLastCall)
    }
  }
}

/**
 * 请求动画帧节流
 * @param func 要节流的函数
 * @returns 节流后的函数
 */
export function rafThrottle<T extends (...args: any[]) => any>(
  func: T
): (...args: Parameters<T>) => void {
  let rafId: number | null = null
  let lastArgs: Parameters<T> | null = null

  return function (this: any, ...args: Parameters<T>) {
    lastArgs = args

    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        if (lastArgs) {
          func.apply(this, lastArgs)
        }
        rafId = null
      })
    }
  }
}

/**
 * 内存化函数（缓存计算结果）
 * @param func 要缓存的函数
 * @param keyGenerator 键生成器
 * @param maxSize 最大缓存大小
 * @returns 缓存后的函数
 */
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  keyGenerator?: (...args: Parameters<T>) => string,
  maxSize = 100
): T & { cache: Map<string, ReturnType<T>>; clear: () => void } {
  const cache = new Map<string, ReturnType<T>>()

  const memoized = function (this: any, ...args: Parameters<T>): ReturnType<T> {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)

    if (cache.has(key)) {
      return cache.get(key)!
    }

    const result = func.apply(this, args)

    // 如果缓存大小超过限制，删除最旧的条目
    if (cache.size >= maxSize) {
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }

    cache.set(key, result)
    return result
  } as T & { cache: Map<string, ReturnType<T>>; clear: () => void }

  memoized.cache = cache
  memoized.clear = () => cache.clear()

  return memoized
}

/**
 * 批处理函数
 * @param func 要批处理的函数
 * @param delay 批处理延迟时间
 * @returns 批处理后的函数
 */
export function batchProcess<T>(
  func: (items: T[]) => void,
  delay = 16
): (item: T) => void {
  let batch: T[] = []
  let timeoutId: ReturnType<typeof setTimeout> | null = null

  return function (item: T) {
    batch.push(item)

    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      if (batch.length > 0) {
        func([...batch])
        batch = []
      }
      timeoutId = null
    }, delay)
  }
}

/**
 * 异步队列处理器
 */
export class AsyncQueue<T> {
  private queue: Array<() => Promise<T>> = []
  private processing = false
  private concurrency: number

  constructor(concurrency = 1) {
    this.concurrency = concurrency
  }

  /**
   * 添加任务到队列
   * @param task 异步任务
   * @returns Promise
   */
  add(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await task()
          resolve(result)
          return result
        } catch (error) {
          reject(error)
          throw error
        }
      })

      this.process()
    })
  }

  /**
   * 处理队列
   */
  private async process() {
    if (this.processing || this.queue.length === 0) {
      return
    }

    this.processing = true

    const tasks = this.queue.splice(0, this.concurrency)
    
    try {
      await Promise.all(tasks.map(task => task()))
    } catch (error) {
      console.error('Queue processing error:', error)
    }

    this.processing = false

    // 继续处理剩余任务
    if (this.queue.length > 0) {
      this.process()
    }
  }

  /**
   * 清空队列
   */
  clear() {
    this.queue = []
  }

  /**
   * 获取队列长度
   */
  get size() {
    return this.queue.length
  }
}

/**
 * 图片预加载
 * @param urls 图片URL数组
 * @param concurrency 并发数
 * @returns Promise
 */
export function preloadImages(urls: string[], concurrency = 3): Promise<void> {
  const queue = new AsyncQueue<void>(concurrency)

  const loadImage = (url: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
      img.src = url
    })
  }

  const promises = urls.map(url => queue.add(() => loadImage(url)))

  return Promise.all(promises).then(() => {})
}

/**
 * 检查是否支持WebP格式
 * @returns Promise<boolean>
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image()
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2)
    }
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
  })
}

/**
 * 获取设备像素比
 * @returns number
 */
export function getDevicePixelRatio(): number {
  return window.devicePixelRatio || 1
}

/**
 * 检查是否为移动设备
 * @returns boolean
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 获取网络连接信息
 * @returns object
 */
export function getNetworkInfo() {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
  
  if (!connection) {
    return {
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
      saveData: false
    }
  }

  return {
    effectiveType: connection.effectiveType || 'unknown',
    downlink: connection.downlink || 0,
    rtt: connection.rtt || 0,
    saveData: connection.saveData || false
  }
}

/**
 * 性能监控
 */
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map()
  private measures: Map<string, number> = new Map()

  /**
   * 标记时间点
   * @param name 标记名称
   */
  mark(name: string) {
    const time = performance.now()
    this.marks.set(name, time)
    
    if (performance.mark) {
      performance.mark(name)
    }
  }

  /**
   * 测量时间间隔
   * @param name 测量名称
   * @param startMark 开始标记
   * @param endMark 结束标记
   * @returns 时间间隔（毫秒）
   */
  measure(name: string, startMark: string, endMark?: string): number {
    const startTime = this.marks.get(startMark)
    const endTime = endMark ? this.marks.get(endMark) : performance.now()

    if (!startTime) {
      console.warn(`Start mark "${startMark}" not found`)
      return 0
    }

    const duration = (endTime || performance.now()) - startTime
    this.measures.set(name, duration)

    if (performance.measure) {
      try {
        performance.measure(name, startMark, endMark)
      } catch (error) {
        console.warn('Performance measure failed:', error)
      }
    }

    return duration
  }

  /**
   * 获取测量结果
   * @param name 测量名称
   * @returns 时间间隔（毫秒）
   */
  getMeasure(name: string): number | undefined {
    return this.measures.get(name)
  }

  /**
   * 清除所有标记和测量
   */
  clear() {
    this.marks.clear()
    this.measures.clear()
    
    if (performance.clearMarks) {
      performance.clearMarks()
    }
    
    if (performance.clearMeasures) {
      performance.clearMeasures()
    }
  }

  /**
   * 获取所有测量结果
   * @returns 测量结果对象
   */
  getAllMeasures(): Record<string, number> {
    return Object.fromEntries(this.measures)
  }
}

// 导出单例实例
export const performanceMonitor = new PerformanceMonitor()
