/**
 * 表单验证工具，包含错误处理
 */

import { handleValidationError } from './errorHandler'

// 验证规则类型
export type ValidationRule = {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

// 验证结果类型
export interface ValidationResult {
  valid: boolean
  errors: string[]
  field?: string
}

// 表单验证结果类型
export interface FormValidationResult {
  valid: boolean
  errors: Record<string, string[]>
  firstError?: string
}

/**
 * 验证单个字段
 */
export function validateField(value: any, rules: ValidationRule[], fieldName?: string): ValidationResult {
  const errors: string[] = []

  for (const rule of rules) {
    // 必填验证
    if (rule.required && (value === null || value === undefined || value === '')) {
      const message = rule.message || `${fieldName || '此字段'}不能为空`
      errors.push(message)
      continue
    }

    // 如果值为空且不是必填，跳过其他验证
    if (!rule.required && (value === null || value === undefined || value === '')) {
      continue
    }

    // 最小长度/值验证
    if (rule.min !== undefined) {
      if (typeof value === 'string' && value.length < rule.min) {
        const message = rule.message || `${fieldName || '此字段'}长度不能少于${rule.min}个字符`
        errors.push(message)
      } else if (typeof value === 'number' && value < rule.min) {
        const message = rule.message || `${fieldName || '此字段'}不能小于${rule.min}`
        errors.push(message)
      }
    }

    // 最大长度/值验证
    if (rule.max !== undefined) {
      if (typeof value === 'string' && value.length > rule.max) {
        const message = rule.message || `${fieldName || '此字段'}长度不能超过${rule.max}个字符`
        errors.push(message)
      } else if (typeof value === 'number' && value > rule.max) {
        const message = rule.message || `${fieldName || '此字段'}不能大于${rule.max}`
        errors.push(message)
      }
    }

    // 正则表达式验证
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        const message = rule.message || `${fieldName || '此字段'}格式不正确`
        errors.push(message)
      }
    }

    // 自定义验证器
    if (rule.validator) {
      const result = rule.validator(value)
      if (result !== true) {
        const message = typeof result === 'string' ? result : (rule.message || `${fieldName || '此字段'}验证失败`)
        errors.push(message)
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    field: fieldName
  }
}

/**
 * 验证整个表单
 */
export function validateForm(
  data: Record<string, any>,
  rules: Record<string, ValidationRule[]>
): FormValidationResult {
  const errors: Record<string, string[]> = {}
  let firstError: string | undefined

  for (const [field, fieldRules] of Object.entries(rules)) {
    const result = validateField(data[field], fieldRules, field)
    
    if (!result.valid) {
      errors[field] = result.errors
      
      if (!firstError) {
        firstError = result.errors[0]
      }
    }
  }

  const valid = Object.keys(errors).length === 0

  // 如果验证失败，记录错误
  if (!valid) {
    handleValidationError(firstError || '表单验证失败', { errors, data })
  }

  return {
    valid,
    errors,
    firstError
  }
}

/**
 * 常用验证规则
 */
export const commonRules = {
  // 必填
  required: (message?: string): ValidationRule => ({
    required: true,
    message
  }),

  // 邮箱
  email: (message?: string): ValidationRule => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: message || '请输入有效的邮箱地址'
  }),

  // 手机号
  phone: (message?: string): ValidationRule => ({
    pattern: /^1[3-9]\d{9}$/,
    message: message || '请输入有效的手机号码'
  }),

  // 密码强度
  password: (message?: string): ValidationRule => ({
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    message: message || '密码至少8位，包含大小写字母和数字'
  }),

  // 用户名
  username: (message?: string): ValidationRule => ({
    pattern: /^[a-zA-Z0-9_]{3,20}$/,
    message: message || '用户名只能包含字母、数字和下划线，长度3-20位'
  }),

  // 中文姓名
  chineseName: (message?: string): ValidationRule => ({
    pattern: /^[\u4e00-\u9fa5]{2,10}$/,
    message: message || '请输入有效的中文姓名'
  }),

  // 身份证号
  idCard: (message?: string): ValidationRule => ({
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    message: message || '请输入有效的身份证号码'
  }),

  // URL
  url: (message?: string): ValidationRule => ({
    pattern: /^https?:\/\/.+/,
    message: message || '请输入有效的URL地址'
  }),

  // 数字范围
  numberRange: (min: number, max: number, message?: string): ValidationRule => ({
    min,
    max,
    validator: (value) => {
      const num = Number(value)
      return !isNaN(num) && num >= min && num <= max
    },
    message: message || `请输入${min}-${max}之间的数字`
  }),

  // 字符串长度
  length: (min: number, max: number, message?: string): ValidationRule => ({
    min,
    max,
    message: message || `长度应在${min}-${max}个字符之间`
  }),

  // 自定义验证
  custom: (validator: (value: any) => boolean | string, message?: string): ValidationRule => ({
    validator,
    message
  })
}

/**
 * 验证器类
 */
export class FormValidator {
  private rules: Record<string, ValidationRule[]> = {}
  private errors: Record<string, string[]> = {}

  /**
   * 添加字段验证规则
   */
  addRule(field: string, rules: ValidationRule[]): this {
    this.rules[field] = rules
    return this
  }

  /**
   * 添加多个字段的验证规则
   */
  addRules(rules: Record<string, ValidationRule[]>): this {
    Object.assign(this.rules, rules)
    return this
  }

  /**
   * 验证单个字段
   */
  validateField(field: string, value: any): ValidationResult {
    const fieldRules = this.rules[field] || []
    const result = validateField(value, fieldRules, field)
    
    if (result.valid) {
      delete this.errors[field]
    } else {
      this.errors[field] = result.errors
    }
    
    return result
  }

  /**
   * 验证所有字段
   */
  validate(data: Record<string, any>): FormValidationResult {
    const result = validateForm(data, this.rules)
    this.errors = result.errors
    return result
  }

  /**
   * 清除验证错误
   */
  clearErrors(field?: string): this {
    if (field) {
      delete this.errors[field]
    } else {
      this.errors = {}
    }
    return this
  }

  /**
   * 获取字段错误
   */
  getFieldErrors(field: string): string[] {
    return this.errors[field] || []
  }

  /**
   * 获取所有错误
   */
  getAllErrors(): Record<string, string[]> {
    return { ...this.errors }
  }

  /**
   * 检查是否有错误
   */
  hasErrors(): boolean {
    return Object.keys(this.errors).length > 0
  }

  /**
   * 获取第一个错误消息
   */
  getFirstError(): string | undefined {
    for (const errors of Object.values(this.errors)) {
      if (errors.length > 0) {
        return errors[0]
      }
    }
    return undefined
  }
}

/**
 * 创建表单验证器实例
 */
export function createValidator(rules?: Record<string, ValidationRule[]>): FormValidator {
  const validator = new FormValidator()
  if (rules) {
    validator.addRules(rules)
  }
  return validator
}

/**
 * 异步验证器
 */
export class AsyncValidator {
  private validators: Map<string, (value: any) => Promise<boolean | string>> = new Map()

  /**
   * 添加异步验证器
   */
  addAsyncValidator(field: string, validator: (value: any) => Promise<boolean | string>): this {
    this.validators.set(field, validator)
    return this
  }

  /**
   * 执行异步验证
   */
  async validateAsync(field: string, value: any): Promise<ValidationResult> {
    const validator = this.validators.get(field)
    
    if (!validator) {
      return { valid: true, errors: [] }
    }

    try {
      const result = await validator(value)
      
      if (result === true) {
        return { valid: true, errors: [] }
      } else {
        const message = typeof result === 'string' ? result : `${field}验证失败`
        return { valid: false, errors: [message], field }
      }
    } catch (error) {
      handleValidationError(`异步验证失败: ${error}`, { field, value, error })
      return { valid: false, errors: ['验证过程中发生错误'], field }
    }
  }

  /**
   * 批量异步验证
   */
  async validateAllAsync(data: Record<string, any>): Promise<FormValidationResult> {
    const errors: Record<string, string[]> = {}
    let firstError: string | undefined

    const promises = Array.from(this.validators.keys()).map(async (field) => {
      const result = await this.validateAsync(field, data[field])
      
      if (!result.valid) {
        errors[field] = result.errors
        
        if (!firstError) {
          firstError = result.errors[0]
        }
      }
    })

    await Promise.all(promises)

    return {
      valid: Object.keys(errors).length === 0,
      errors,
      firstError
    }
  }
}
