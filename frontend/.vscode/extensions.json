{
  "recommendations": [
    // Vue开发必备
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",

    // 代码格式化和检查
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "stylelint.vscode-stylelint",

    // TypeScript支持
    "ms-vscode.vscode-typescript-next",

    // 代码质量
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "aaron-bond.better-comments",

    // Git工具
    "eamodio.gitlens",
    "mhutchie.git-graph",

    // 开发工具
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "christian-kohler.npm-intellisense",

    // 测试工具
    "ms-playwright.playwright",
    "vitest.explorer",

    // 主题和图标
    "PKief.material-icon-theme",
    "GitHub.github-vscode-theme",

    // 实用工具
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.live-server",

    // 代码片段
    "hollowtree.vue-snippets",
    "sdras.vue-vscode-snippets",

    // 调试工具
    "ms-vscode.js-debug",

    // 文档工具
    "yzhang.markdown-all-in-one",

    // API开发
    "humao.rest-client",

    // 协作工具
    "ms-vsliveshare.vsliveshare",

    // 文件管理
    "sleistner.vscode-fileutils",
    "formulahendry.auto-close-tag",

    // 颜色工具
    "naumovs.color-highlight",

    // 代码统计
    "VisualStudioExptTeam.vscodeintellicode"
  ],
  "unwantedRecommendations": [
    // 避免与Volar冲突的扩展
    "octref.vetur",
    "ms-vscode.vscode-typescript-vue-plugin-pack"
  ]
}
