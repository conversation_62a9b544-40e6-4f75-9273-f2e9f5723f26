# Frontend模块依赖分析工具使用说明

## 📁 文件说明

本次分析生成了以下文件：

### 1. 数据文件
- **`frontend-dependency-mindmap.json`** - 完整的模块依赖关系数据 (Cytoscape.js格式)
- **`frontend-core-dependencies.json`** - 核心依赖关系简化版数据
- **`frontend-dependency-analysis-report.md`** - 详细分析报告

### 2. 可视化文件
- **`frontend-dependency-visualization.html`** - 交互式思维导图可视化界面

## 🚀 快速开始

### 查看可视化思维导图

1. **直接打开HTML文件**
   ```bash
   # 在浏览器中打开
   open frontend-dependency-visualization.html
   ```

2. **使用本地服务器** (推荐)
   ```bash
   # 使用Python启动简单服务器
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 然后访问 http://localhost:8000/frontend-dependency-visualization.html
   ```

### 在项目中使用数据

```javascript
// 加载完整依赖数据
fetch('./frontend-dependency-mindmap.json')
  .then(response => response.json())
  .then(data => {
    // 使用Cytoscape.js渲染
    const cy = cytoscape({
      container: document.getElementById('cy'),
      elements: [...data.nodes, ...data.edges],
      // ... 其他配置
    });
  });

// 加载核心依赖数据
fetch('./frontend-core-dependencies.json')
  .then(response => response.json())
  .then(data => {
    // 数据包含metadata和推荐建议
    console.log(data.metadata.recommendations);
  });
```

## 🎛️ 可视化界面功能

### 交互功能
- **节点点击**: 显示模块详细信息
- **拖拽**: 自由移动节点位置
- **缩放**: 鼠标滚轮缩放视图
- **平移**: 拖拽空白区域平移视图

### 控制面板
- **布局切换**: 5种不同的布局算法
  - 层次布局 (Dagre) - 推荐用于依赖关系展示
  - 圆形布局 - 适合查看模块分布
  - 网格布局 - 整齐排列
  - 同心圆布局 - 按重要性分层
  - 广度优先 - 按依赖深度排列

- **视图控制**
  - 适应视图 - 自动调整到最佳视角
  - 重置缩放 - 恢复默认缩放级别
  - 导出图片 - 保存为PNG格式
  - 切换连线标签 - 显示/隐藏依赖关系标签

### 信息面板
- **模块信息**: 点击节点查看详细信息
- **依赖列表**: 显示模块的输入和输出依赖
- **统计数据**: 实时显示图表统计信息

## 📊 数据结构说明

### 节点 (Nodes) 数据格式
```json
{
  "data": {
    "id": "unique-id",
    "label": "显示名称",
    "type": "模块类型",
    "description": "模块描述",
    "dependencies": ["依赖列表"],
    "exports": ["导出列表"],
    "fileCount": "文件数量",
    "size": "节点大小",
    "color": "节点颜色"
  }
}
```

### 边 (Edges) 数据格式
```json
{
  "data": {
    "id": "edge-id",
    "source": "源节点ID",
    "target": "目标节点ID",
    "type": "关系类型",
    "label": "关系描述",
    "strength": "关系强度"
  }
}
```

### 模块类型分类
- **entry**: 应用入口点
- **module-group**: 模块组
- **store**: Pinia状态管理
- **service**: 业务逻辑服务
- **types**: TypeScript类型定义
- **util**: 工具函数
- **component-group**: 组件组
- **view**: 页面组件
- **config**: 配置文件

### 关系类型分类
- **import**: 导入依赖
- **contains**: 包含关系
- **uses**: 使用关系
- **creates**: 创建关系
- **routes-to**: 路由关系

## 🔧 自定义和扩展

### 修改样式
编辑HTML文件中的CSS样式或Cytoscape样式配置：

```javascript
// 修改节点样式
style: [
  {
    selector: 'node',
    style: {
      'background-color': 'data(color)',
      'width': 'data(size)',
      'height': 'data(size)',
      // ... 其他样式
    }
  }
]
```

### 添加新的布局算法
```javascript
// 在changeLayout函数中添加新的布局选项
case 'custom-layout':
  layoutOptions.name = 'custom';
  layoutOptions.customParam = 'value';
  break;
```

### 扩展数据分析
```javascript
// 添加新的统计指标
function calculateCustomMetrics() {
  const nodes = cy.nodes();
  // 自定义分析逻辑
  return metrics;
}
```

## 📈 分析建议应用

### 基于分析结果的优化建议

1. **重构高耦合模块**
   - 识别连接数过多的节点
   - 考虑拆分或抽象

2. **优化依赖路径**
   - 减少深层依赖链
   - 避免循环依赖

3. **模块边界清晰化**
   - 明确各层职责
   - 统一接口规范

### 持续监控
- 定期重新生成依赖图
- 跟踪模块复杂度变化
- 监控新增依赖的合理性

## 🛠️ 故障排除

### 常见问题

1. **JSON文件加载失败**
   - 确保文件路径正确
   - 使用本地服务器而非直接打开文件

2. **图表显示异常**
   - 检查浏览器控制台错误信息
   - 确认Cytoscape.js库加载成功

3. **性能问题**
   - 大型项目可考虑数据分页
   - 使用简化版数据文件

### 浏览器兼容性
- Chrome 80+ (推荐)
- Firefox 75+
- Safari 13+
- Edge 80+

## 📝 更新和维护

### 重新生成分析数据
当项目结构发生变化时，需要重新运行分析：

```bash
# 使用Augment Agent重新分析
# 或手动更新JSON数据文件
```

### 版本控制
建议将生成的文件加入版本控制，以跟踪项目架构演进：

```gitignore
# 可选择性忽略某些文件
# frontend-dependency-*.json
```

---

**工具版本**: Augment Agent v1.0  
**生成时间**: 2025-08-01  
**支持**: 如有问题请查看分析报告或联系开发团队
