{"nodes": [{"data": {"id": "main", "label": "main.ts", "type": "entry", "description": "应用入口点", "dependencies": ["vue", "pinia", "vue-router", "element-plus"], "size": "large", "color": "#FF6B6B"}}, {"data": {"id": "app", "label": "App.vue", "type": "root-component", "description": "根组件", "dependencies": ["stores/app", "components/layout", "components/common"], "size": "large", "color": "#4ECDC4"}}, {"data": {"id": "stores", "label": "Stores (状态管理)", "type": "module-group", "description": "Pinia状态管理模块", "fileCount": 8, "size": "large", "color": "#45B7D1"}}, {"data": {"id": "stores-app", "label": "app.ts", "type": "store", "description": "应用全局状态", "exports": ["useAppStore"], "dependencies": ["pinia", "vue"], "size": "medium", "color": "#96CEB4"}}, {"data": {"id": "stores-user", "label": "user.ts", "type": "store", "description": "用户状态管理", "exports": ["useUserStore"], "dependencies": ["pinia", "vue"], "size": "medium", "color": "#96CEB4"}}, {"data": {"id": "stores-task", "label": "task.ts", "type": "store", "description": "任务状态管理", "exports": ["useTaskStore"], "dependencies": ["pinia", "vue", "types", "mock/taskData", "utils/errorHandler"], "size": "medium", "color": "#96CEB4"}}, {"data": {"id": "stores-review", "label": "review.ts", "type": "store", "description": "复习状态管理", "exports": ["useReviewStore"], "dependencies": ["pinia", "vue", "types", "mock/reviewData"], "size": "medium", "color": "#96CEB4"}}, {"data": {"id": "stores-learning", "label": "learning.ts", "type": "store", "description": "学习数据状态", "exports": ["useLearningStore"], "dependencies": ["pinia", "vue", "services/TimeEstimationService"], "size": "medium", "color": "#96CEB4"}}, {"data": {"id": "stores-notification", "label": "notification.ts", "type": "store", "description": "通知状态管理", "exports": ["useNotificationStore"], "dependencies": ["pinia", "vue"], "size": "medium", "color": "#96CEB4"}}, {"data": {"id": "stores-settings", "label": "settings.ts", "type": "store", "description": "设置状态管理", "exports": ["useSettingsStore"], "dependencies": ["pinia", "vue"], "size": "medium", "color": "#96CEB4"}}, {"data": {"id": "services", "label": "Services (业务逻辑)", "type": "module-group", "description": "业务逻辑服务层", "fileCount": 3, "size": "large", "color": "#F7DC6F"}}, {"data": {"id": "services-ebbinghaus", "label": "EbbinghausAlgorithm.ts", "type": "service", "description": "艾宾浩斯记忆算法", "exports": ["EbbinghausAlgorithm", "ReviewSession", "LearningProgress"], "dependencies": [], "size": "medium", "color": "#F39C12"}}, {"data": {"id": "services-time-estimation", "label": "TimeEstimationService.ts", "type": "service", "description": "智能时间预估服务", "exports": ["TimeEstimationService", "TaskFeatures", "TimeEstimationResult"], "dependencies": [], "size": "medium", "color": "#F39C12"}}, {"data": {"id": "services-notification", "label": "NotificationService.ts", "type": "service", "description": "通知服务", "exports": ["NotificationService", "NotificationOptions", "InAppMessage"], "dependencies": [], "size": "medium", "color": "#F39C12"}}, {"data": {"id": "types", "label": "Types (类型定义)", "type": "module-group", "description": "TypeScript类型定义", "fileCount": 2, "size": "large", "color": "#BB8FCE"}}, {"data": {"id": "types-index", "label": "index.ts", "type": "types", "description": "核心业务类型", "exports": ["Task", "ReviewItem", "Subject", "TaskStatus", "ApiResponse"], "dependencies": [], "size": "medium", "color": "#8E44AD"}}, {"data": {"id": "types-mindmap", "label": "mindmap.ts", "type": "types", "description": "思维导图类型", "exports": ["MindMapNode", "MindMapEdge", "MindMap", "MindMapSettings"], "dependencies": [], "size": "medium", "color": "#8E44AD"}}, {"data": {"id": "utils", "label": "Utils (工具函数)", "type": "module-group", "description": "通用工具函数", "fileCount": 4, "size": "large", "color": "#85C1E9"}}, {"data": {"id": "utils-error-handler", "label": "errorHandler.ts", "type": "util", "description": "全局错误处理", "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleError", "handleNetworkError"], "dependencies": ["element-plus"], "size": "medium", "color": "#3498DB"}}, {"data": {"id": "utils-http-client", "label": "httpClient.ts", "type": "util", "description": "HTTP客户端", "exports": ["HttpClient", "httpClient", "get", "post"], "dependencies": ["axios", "element-plus", "utils/errorHandler"], "size": "medium", "color": "#3498DB"}}, {"data": {"id": "utils-performance", "label": "performance.ts", "type": "util", "description": "性能监控工具", "exports": ["memoize", "debounce", "throttle", "PerformanceMonitor"], "dependencies": [], "size": "medium", "color": "#3498DB"}}, {"data": {"id": "utils-validation", "label": "validation.ts", "type": "util", "description": "表单验证工具", "exports": ["validateField", "validateForm", "FormValidator"], "dependencies": ["utils/errorHandler"], "size": "medium", "color": "#3498DB"}}, {"data": {"id": "components", "label": "Components (UI组件)", "type": "module-group", "description": "可复用UI组件", "fileCount": 20, "size": "large", "color": "#F8C471"}}, {"data": {"id": "components-layout", "label": "layout/", "type": "component-group", "description": "布局组件", "exports": ["AppLayout", "MobileLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AppSidebar"], "dependencies": ["stores/app", "vue-router"], "size": "medium", "color": "#E67E22"}}, {"data": {"id": "components-common", "label": "common/", "type": "component-group", "description": "通用组件", "exports": ["Error<PERSON>ou<PERSON><PERSON>", "NetworkStatus", "InAppNotification"], "dependencies": ["stores/app", "stores/notification"], "size": "medium", "color": "#E67E22"}}, {"data": {"id": "components-task", "label": "task/", "type": "component-group", "description": "任务相关组件", "exports": ["TaskCard", "TaskCreateForm", "LoadDashboard"], "dependencies": ["stores/task", "types", "utils/performance"], "size": "medium", "color": "#E67E22"}}, {"data": {"id": "components-mindmap", "label": "mindmap/", "type": "component-group", "description": "思维导图组件", "exports": ["MindMapCanvas", "CytoscapeMindMap", "MindMapNodeTree"], "dependencies": ["types/mindmap", "cytoscape"], "size": "medium", "color": "#E67E22"}}, {"data": {"id": "views", "label": "Views (页面组件)", "type": "module-group", "description": "页面级组件", "fileCount": 7, "size": "large", "color": "#AED6F1"}}, {"data": {"id": "views-dashboard", "label": "Dashboard.vue", "type": "view", "description": "学习概览页面", "dependencies": ["stores/app", "stores/task", "stores/learning"], "size": "medium", "color": "#5DADE2"}}, {"data": {"id": "views-tasks", "label": "Tasks.vue", "type": "view", "description": "任务管理页面", "dependencies": ["stores/task", "components/task", "types"], "size": "medium", "color": "#5DADE2"}}, {"data": {"id": "views-mindmap", "label": "MindMap.vue", "type": "view", "description": "思维导图页面", "dependencies": ["components/mindmap", "types/mindmap"], "size": "medium", "color": "#5DADE2"}}, {"data": {"id": "router", "label": "Router (路由配置)", "type": "config", "description": "Vue Router路由配置", "exports": ["routes"], "dependencies": ["vue-router", "views/*"], "size": "medium", "color": "#D7BDE2"}}, {"data": {"id": "mock", "label": "<PERSON><PERSON> (模拟数据)", "type": "module-group", "description": "开发阶段模拟数据", "fileCount": 2, "size": "medium", "color": "#F5B7B1"}}], "edges": [{"data": {"id": "main-app", "source": "main", "target": "app", "type": "import", "label": "imports"}}, {"data": {"id": "main-stores", "source": "main", "target": "stores", "type": "import", "label": "creates pinia"}}, {"data": {"id": "main-router", "source": "main", "target": "router", "type": "import", "label": "imports routes"}}, {"data": {"id": "main-utils", "source": "main", "target": "utils-error-handler", "type": "import", "label": "error handling"}}, {"data": {"id": "app-stores-app", "source": "app", "target": "stores-app", "type": "import", "label": "uses store"}}, {"data": {"id": "app-components-layout", "source": "app", "target": "components-layout", "type": "import", "label": "layout components"}}, {"data": {"id": "app-components-common", "source": "app", "target": "components-common", "type": "import", "label": "common components"}}, {"data": {"id": "stores-stores-app", "source": "stores", "target": "stores-app", "type": "contains", "label": "contains"}}, {"data": {"id": "stores-stores-user", "source": "stores", "target": "stores-user", "type": "contains", "label": "contains"}}, {"data": {"id": "stores-stores-task", "source": "stores", "target": "stores-task", "type": "contains", "label": "contains"}}, {"data": {"id": "stores-stores-review", "source": "stores", "target": "stores-review", "type": "contains", "label": "contains"}}, {"data": {"id": "stores-stores-learning", "source": "stores", "target": "stores-learning", "type": "contains", "label": "contains"}}, {"data": {"id": "stores-stores-notification", "source": "stores", "target": "stores-notification", "type": "contains", "label": "contains"}}, {"data": {"id": "stores-stores-settings", "source": "stores", "target": "stores-settings", "type": "contains", "label": "contains"}}, {"data": {"id": "stores-task-types", "source": "stores-task", "target": "types-index", "type": "import", "label": "imports types"}}, {"data": {"id": "stores-task-utils", "source": "stores-task", "target": "utils-error-handler", "type": "import", "label": "error handling"}}, {"data": {"id": "stores-task-mock", "source": "stores-task", "target": "mock", "type": "import", "label": "mock data"}}, {"data": {"id": "stores-review-types", "source": "stores-review", "target": "types-index", "type": "import", "label": "imports types"}}, {"data": {"id": "stores-review-mock", "source": "stores-review", "target": "mock", "type": "import", "label": "mock data"}}, {"data": {"id": "stores-learning-services", "source": "stores-learning", "target": "services-time-estimation", "type": "import", "label": "time estimation"}}, {"data": {"id": "services-services-ebbinghaus", "source": "services", "target": "services-ebbinghaus", "type": "contains", "label": "contains"}}, {"data": {"id": "services-services-time", "source": "services", "target": "services-time-estimation", "type": "contains", "label": "contains"}}, {"data": {"id": "services-services-notification", "source": "services", "target": "services-notification", "type": "contains", "label": "contains"}}, {"data": {"id": "types-types-index", "source": "types", "target": "types-index", "type": "contains", "label": "contains"}}, {"data": {"id": "types-types-mindmap", "source": "types", "target": "types-mindmap", "type": "contains", "label": "contains"}}, {"data": {"id": "utils-utils-error", "source": "utils", "target": "utils-error-handler", "type": "contains", "label": "contains"}}, {"data": {"id": "utils-utils-http", "source": "utils", "target": "utils-http-client", "type": "contains", "label": "contains"}}, {"data": {"id": "utils-utils-performance", "source": "utils", "target": "utils-performance", "type": "contains", "label": "contains"}}, {"data": {"id": "utils-utils-validation", "source": "utils", "target": "utils-validation", "type": "contains", "label": "contains"}}, {"data": {"id": "utils-http-error", "source": "utils-http-client", "target": "utils-error-handler", "type": "import", "label": "error handling"}}, {"data": {"id": "utils-validation-error", "source": "utils-validation", "target": "utils-error-handler", "type": "import", "label": "error handling"}}, {"data": {"id": "components-components-layout", "source": "components", "target": "components-layout", "type": "contains", "label": "contains"}}, {"data": {"id": "components-components-common", "source": "components", "target": "components-common", "type": "contains", "label": "contains"}}, {"data": {"id": "components-components-task", "source": "components", "target": "components-task", "type": "contains", "label": "contains"}}, {"data": {"id": "components-components-mindmap", "source": "components", "target": "components-mindmap", "type": "contains", "label": "contains"}}, {"data": {"id": "components-task-stores", "source": "components-task", "target": "stores-task", "type": "import", "label": "uses store"}}, {"data": {"id": "components-task-types", "source": "components-task", "target": "types-index", "type": "import", "label": "imports types"}}, {"data": {"id": "components-mindmap-types", "source": "components-mindmap", "target": "types-mindmap", "type": "import", "label": "imports types"}}, {"data": {"id": "views-views-dashboard", "source": "views", "target": "views-dashboard", "type": "contains", "label": "contains"}}, {"data": {"id": "views-views-tasks", "source": "views", "target": "views-tasks", "type": "contains", "label": "contains"}}, {"data": {"id": "views-views-mindmap", "source": "views", "target": "views-mindmap", "type": "contains", "label": "contains"}}, {"data": {"id": "views-tasks-stores", "source": "views-tasks", "target": "stores-task", "type": "import", "label": "uses store"}}, {"data": {"id": "views-tasks-components", "source": "views-tasks", "target": "components-task", "type": "import", "label": "uses components"}}, {"data": {"id": "views-mindmap-components", "source": "views-mindmap", "target": "components-mindmap", "type": "import", "label": "uses components"}}, {"data": {"id": "router-views", "source": "router", "target": "views", "type": "import", "label": "lazy imports"}}]}