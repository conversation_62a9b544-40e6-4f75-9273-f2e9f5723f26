{"nodes": [{"data": {"id": "main", "label": "main.ts\n应用入口", "type": "entry", "importance": 5, "connections": 4, "color": "#FF6B6B", "size": 80}}, {"data": {"id": "stores", "label": "Stores\n状态管理中心", "type": "state-management", "importance": 5, "connections": 8, "color": "#45B7D1", "size": 100}}, {"data": {"id": "services", "label": "Services\n业务逻辑层", "type": "business-logic", "importance": 4, "connections": 3, "color": "#F7DC6F", "size": 80}}, {"data": {"id": "types", "label": "Types\n类型定义", "type": "type-definitions", "importance": 4, "connections": 6, "color": "#BB8FCE", "size": 70}}, {"data": {"id": "utils", "label": "Utils\n工具函数", "type": "utilities", "importance": 4, "connections": 5, "color": "#85C1E9", "size": 70}}, {"data": {"id": "components", "label": "Components\nUI组件库", "type": "ui-components", "importance": 3, "connections": 4, "color": "#F8C471", "size": 80}}, {"data": {"id": "views", "label": "Views\n页面组件", "type": "page-components", "importance": 3, "connections": 3, "color": "#AED6F1", "size": 70}}, {"data": {"id": "router", "label": "Router\n路由配置", "type": "routing", "importance": 3, "connections": 2, "color": "#D7BDE2", "size": 60}}, {"data": {"id": "error-handler", "label": "ErrorHandler\n错误处理核心", "type": "error-handling", "importance": 5, "connections": 5, "color": "#F1948A", "size": 70}}, {"data": {"id": "task-store", "label": "TaskStore\n任务管理", "type": "core-store", "importance": 4, "connections": 4, "color": "#96CEB4", "size": 70}}, {"data": {"id": "ebb<PERSON><PERSON>", "label": "Ebbinghaus\n记忆算法", "type": "core-algorithm", "importance": 4, "connections": 2, "color": "#F39C12", "size": 70}}, {"data": {"id": "mindmap-components", "label": "MindMap\n思维导图组件", "type": "feature-component", "importance": 3, "connections": 2, "color": "#E67E22", "size": 60}}], "edges": [{"data": {"id": "main-stores", "source": "main", "target": "stores", "type": "creates", "label": "创建Pinia实例", "strength": "strong", "color": "#2C3E50"}}, {"data": {"id": "main-router", "source": "main", "target": "router", "type": "imports", "label": "导入路由", "strength": "strong", "color": "#2C3E50"}}, {"data": {"id": "main-error-handler", "source": "main", "target": "error-handler", "type": "configures", "label": "配置错误处理", "strength": "strong", "color": "#2C3E50"}}, {"data": {"id": "stores-types", "source": "stores", "target": "types", "type": "imports", "label": "类型定义", "strength": "strong", "color": "#7F8C8D"}}, {"data": {"id": "stores-utils", "source": "stores", "target": "utils", "type": "imports", "label": "工具函数", "strength": "medium", "color": "#7F8C8D"}}, {"data": {"id": "stores-services", "source": "stores", "target": "services", "type": "imports", "label": "业务逻辑", "strength": "medium", "color": "#7F8C8D"}}, {"data": {"id": "stores-task-store", "source": "stores", "target": "task-store", "type": "contains", "label": "包含", "strength": "strong", "color": "#27AE60"}}, {"data": {"id": "services-ebbinghaus", "source": "services", "target": "ebb<PERSON><PERSON>", "type": "contains", "label": "包含", "strength": "strong", "color": "#27AE60"}}, {"data": {"id": "task-store-error-handler", "source": "task-store", "target": "error-handler", "type": "uses", "label": "错误处理", "strength": "medium", "color": "#E74C3C"}}, {"data": {"id": "components-stores", "source": "components", "target": "stores", "type": "uses", "label": "状态管理", "strength": "strong", "color": "#3498DB"}}, {"data": {"id": "components-types", "source": "components", "target": "types", "type": "imports", "label": "类型定义", "strength": "strong", "color": "#7F8C8D"}}, {"data": {"id": "components-mindmap", "source": "components", "target": "mindmap-components", "type": "contains", "label": "包含", "strength": "strong", "color": "#27AE60"}}, {"data": {"id": "views-components", "source": "views", "target": "components", "type": "uses", "label": "使用组件", "strength": "strong", "color": "#3498DB"}}, {"data": {"id": "views-stores", "source": "views", "target": "stores", "type": "uses", "label": "状态管理", "strength": "strong", "color": "#3498DB"}}, {"data": {"id": "router-views", "source": "router", "target": "views", "type": "routes-to", "label": "路由到", "strength": "strong", "color": "#9B59B6"}}, {"data": {"id": "utils-error-handler", "source": "utils", "target": "error-handler", "type": "contains", "label": "包含", "strength": "strong", "color": "#27AE60"}}], "layout": {"name": "dagre", "rankDir": "TB", "spacingFactor": 2, "nodeDimensionsIncludeLabels": true, "animate": true, "animationDuration": 1000}, "style": {"node": {"shape": "round-rectangle", "borderWidth": 3, "borderColor": "#34495E", "textWrap": "wrap", "textMaxWidth": "120px", "fontSize": "12px", "fontWeight": "bold", "textColor": "#2C3E50"}, "edge": {"width": 3, "curveStyle": "bezier", "targetArrowShape": "triangle", "arrowScale": 1.5, "fontSize": "10px", "textRotation": "autorotate"}}, "metadata": {"title": "Frontend核心依赖关系图", "description": "展示前端项目核心模块间的依赖关系和数据流", "version": "1.0.0", "created": "2025-08-01", "totalNodes": 12, "totalEdges": 16, "complexity": "medium", "recommendations": ["考虑将mock数据访问抽象为API层", "优化错误处理的统一性", "增加核心模块的单元测试覆盖", "考虑实现依赖注入减少耦合"]}}